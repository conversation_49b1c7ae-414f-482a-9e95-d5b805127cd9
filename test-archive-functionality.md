# Archive Functionality Test Plan

## Manual Testing Steps

### 1. Test Delete Folder Functionality
1. Navigate to the Tasks section in the sidebar
2. In the folder tree, right-click on any folder
3. Verify "Delete Folder" option appears in the context menu with red text
4. Click "Delete Folder"
5. Confirm the deletion in the confirmation dialog
6. Verify the folder and all its contents disappear from the tree

### 2. Test Delete Project Functionality
1. In the folder tree, right-click on any project
2. Verify "Delete Project" option appears in the context menu with red text
3. Click "Delete Project"
4. Confirm the deletion in the confirmation dialog
5. Verify the project and all its tasks disappear from the tree

### 3. Test Admin Archive Access
1. Ensure you have admin privileges (check user_profiles table: role = 'admin')
2. Navigate to Admin Settings → Archive
3. Verify the Archive section appears in the admin menu
4. Check that archived items appear in the archive list
5. Verify archive statistics are displayed correctly

### 4. Test Restore Functionality
1. In the Archive section, find a previously deleted item
2. Click the "Restore" button
3. Confirm the restoration
4. Navigate back to the Tasks section
5. Verify the item appears back in its original location

### 5. Test Empty Archive Functionality
1. In the Archive section, click "Empty Archive"
2. Confirm the action twice (as required)
3. Verify all archived items are permanently deleted
4. Check that archive statistics show zero items

## Database Verification

### Check Archive Columns
```sql
-- Verify archive columns exist
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name IN ('folders', 'projects', 'tasks') 
AND (column_name LIKE '%archive%' OR column_name LIKE '%original%')
ORDER BY table_name, column_name;
```

### Check Archive Functions
```sql
-- Verify all archive functions exist
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND (routine_name LIKE '%archive%' OR routine_name LIKE '%restore%' OR routine_name = 'cleanup_expired_archives');
```

### Test Archive Function
```sql
-- Test archiving a folder (replace with actual folder ID and user ID)
SELECT archive_folder_cascade('FOLDER_ID_HERE', 'USER_ID_HERE');

-- Check if folder was archived
SELECT id, name, archived_at, archived_by FROM folders WHERE archived_at IS NOT NULL;
```

### Test Restore Function
```sql
-- Test restoring a folder (replace with actual folder ID)
SELECT restore_folder_cascade('FOLDER_ID_HERE');

-- Check if folder was restored
SELECT id, name, archived_at, archived_by FROM folders WHERE id = 'FOLDER_ID_HERE';
```

## Expected Results

### UI Behavior
- Delete options appear in context menus with red styling
- Confirmation dialogs appear before deletion
- Items disappear from normal views after deletion
- Archive section is only visible to admin users
- Restore functionality works correctly
- Empty archive removes all items permanently

### Database Behavior
- Archived items have `archived_at` timestamp and `archived_by` user ID
- Original location is preserved in `original_*` columns
- RLS policies hide archived items from normal queries
- Admin users can see archived items
- Restore functions correctly move items back to original locations
- Cleanup function removes expired items (after 7 days)

## Troubleshooting

### If Delete Buttons Don't Appear
- Check if user has proper permissions
- Verify TaskTreeNode component is updated
- Check console for JavaScript errors

### If Archive Section Doesn't Appear
- Verify user has admin role in database
- Check SupabaseSidebar component integration
- Verify ArchiveManager component is imported correctly

### If Database Functions Fail
- Check Supabase function logs
- Verify RLS policies are correctly configured
- Ensure user authentication is working

### If Items Don't Archive Properly
- Check archiveService implementation
- Verify database function parameters
- Check for foreign key constraint issues

## Performance Considerations

### Large Datasets
- Archive operations may take time with many nested items
- Consider adding progress indicators for large operations
- Monitor database performance during cascade operations

### Cleanup Schedule
- Implement automated cleanup job for expired archives
- Consider running cleanup during off-peak hours
- Monitor storage usage of archived items

## Security Verification

### Admin-Only Access
- Verify non-admin users cannot access archive section
- Test that archive functions require proper permissions
- Ensure RLS policies prevent unauthorized access to archived data

### Data Integrity
- Verify cascade operations maintain referential integrity
- Test restore operations preserve original relationships
- Ensure cleanup operations don't leave orphaned data
