# Task Sharing Guide

## 🔗 Overview

The task sharing functionality allows users to generate shareable links that provide direct access to specific tasks or subtasks. Recipients can access the shared task through a URL, with automatic authentication handling.

## 🚀 How to Share Tasks

### Sharing a Task
1. Open any task in edit mode
2. Click the three-dot menu (⋮) in the top-right corner
3. Select **"Share Task"**
4. The shareable link is automatically copied to your clipboard
5. Share the link with team members or stakeholders

### Sharing a Subtask
1. Open any subtask in edit mode
2. Click the three-dot menu (⋮) in the top-right corner
3. Select **"Share Subtask"**
4. The shareable link is automatically copied to your clipboard
5. Share the link with team members or stakeholders

## 🔗 Link Format

Shared links follow this format:
```
http://your-domain.com/#tasks?taskId=uuid
http://your-domain.com/#tasks?taskId=uuid&subtaskId=uuid
```

## 🔐 Security & Access Control

- **Authentication Required**: Recipients must be logged in to access shared tasks
- **Permission-Based**: Users can only access tasks they have permission to view
- **RLS Protection**: All existing Row Level Security policies apply to shared links
- **Automatic Redirect**: Unauthenticated users are redirected to login, then back to the task

## 🎯 User Experience

### For Authenticated Users
- Click shared link → Direct access to task edit screen
- No additional authentication required

### For Unauthenticated Users
- Click shared link → Redirected to login page
- After successful login → Automatically redirected to the shared task
- URL parameters preserved throughout the authentication flow

## 🛠 Technical Implementation

### URL Navigation System
- Hash-based navigation: `#tasks?taskId=uuid`
- Automatic task opening via URL parameters
- Session storage for authentication redirects

### Integration Points
- **TaskForm**: Three-dot menu with "Share Task" option
- **SubtaskForm**: Three-dot menu with "Share Subtask" option
- **Inbox**: Fixed "View Task" links using the same navigation system

## 🐛 Troubleshooting

### Link Not Working
- Verify the recipient has access to the task
- Check that the task hasn't been deleted or archived
- Ensure the recipient has a valid account

### Authentication Issues
- Clear browser cache and cookies
- Try opening the link in an incognito/private window
- Verify Supabase authentication is working properly

### URL Parameters Lost
- This issue has been resolved in the latest version
- Ensure you're using the updated authentication flow

## 📱 Browser Compatibility

- **Clipboard API**: Modern browsers support automatic copying
- **Fallback**: Older browsers show a prompt dialog with the link
- **Cross-Platform**: Works on desktop and mobile browsers

## 🔄 Related Features

- **Clone Tasks**: Available in the same three-dot menu
- **Inbox Navigation**: Uses the same URL navigation system
- **Task Dependencies**: Shared tasks maintain their dependency relationships
- **Custom Fields**: All custom field data is preserved in shared tasks

## 📚 See Also

- [Multi-User Collaboration Guide](./multi-user-collaboration-guide.md)
- [Clone Functionality Guide](./clone-functionality-guide.md)
- [Real-time Collaboration Guide](./real-time-collaboration-guide.md)
