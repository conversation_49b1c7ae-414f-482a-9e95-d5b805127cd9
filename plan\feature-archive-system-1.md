---
goal: Implement Archive System for Folder and Project Deletion with Admin Restore Functionality
version: 1.0
date_created: 2025-01-09
last_updated: 2025-01-09
owner: Development Team
tags: [feature, archive, admin, deletion, restore]
---

# Introduction

This plan implements a comprehensive archive system that allows users to delete folders and projects while providing administrators with the ability to restore them within a 1-week retention period. The system follows application-level validation principles and maintains data integrity while providing a safety net for accidental deletions.

## 1. Requirements & Constraints

- **REQ-001**: Users must be able to delete folders and projects from the task list view
- **REQ-002**: Deleted items must be archived for 1 week before permanent deletion
- **REQ-003**: Only administrators can access and manage the archive section
- **REQ-004**: Administrators must be able to restore archived items to their original locations
- **REQ-005**: Administrators must be able to empty the entire archive with one click
- **REQ-006**: Folder deletion must cascade to all subfolders, projects, and tasks within
- **REQ-007**: Project deletion must cascade to all tasks within the project
- **REQ-008**: Archive system must maintain referential integrity during restore operations
- **SEC-001**: Archive access must be restricted to admin users only
- **SEC-002**: Restore operations must validate admin permissions
- **CON-001**: Must not break existing functionality or database constraints
- **CON-002**: Must follow application-level validation over complex RLS policies
- **CON-003**: Must implement modular components that don't disturb existing codebase
- **GUD-001**: Follow existing code patterns and component structure
- **GUD-002**: Use consistent UI/UX patterns with existing admin sections
- **PAT-001**: Implement soft delete pattern with archived_at timestamp
- **PAT-002**: Use service layer pattern for archive operations

## 2. Implementation Steps

### Phase 1: Database Schema Updates
1. Add archive-related columns to existing tables (folders, projects, tasks)
2. Create archive management functions and triggers
3. Update RLS policies to handle archived items

### Phase 2: Service Layer Implementation
1. Create archiveService for managing archive operations
2. Update existing services to handle archive functionality
3. Implement cascade archive logic for folders and projects

### Phase 3: UI Components Development
1. Add delete buttons to context menus in TaskTreeNode
2. Create Archive admin section component
3. Implement restore and empty archive functionality
4. Add confirmation dialogs for delete operations

### Phase 4: Integration and Testing
1. Integrate archive functionality with existing stores
2. Update admin sidebar to include Archive section
3. Test cascade operations and restore functionality
4. Implement automatic cleanup after 1 week

## 3. Alternatives

- **ALT-001**: Hard delete with backup export - Rejected due to complexity and data loss risk
- **ALT-002**: Move to separate archive tables - Rejected due to referential integrity complexity
- **ALT-003**: Use database-level soft delete triggers - Rejected to follow application-level validation preference

## 4. Dependencies

- **DEP-001**: Existing admin authentication system (isUserAdmin function)
- **DEP-002**: Current Supabase service layer architecture
- **DEP-003**: TaskTreeNode context menu system
- **DEP-004**: SupabaseSidebar admin section structure

## 5. Files

- **FILE-001**: supabase-archive-schema.sql - Database schema updates for archive system
- **FILE-002**: src/services/archiveService.ts - Core archive management service
- **FILE-003**: src/components/ArchiveManager.tsx - Admin archive management interface
- **FILE-004**: src/components/TaskTreeNode.tsx - Add delete context menu options
- **FILE-005**: src/components/SupabaseSidebar.tsx - Add Archive admin section
- **FILE-006**: src/store/useSupabaseStore.ts - Integrate archive operations
- **FILE-007**: src/types/index.ts - Add archive-related type definitions

## 6. Testing

- **TEST-001**: Verify folder deletion cascades to subfolders, projects, and tasks
- **TEST-002**: Verify project deletion cascades to all tasks
- **TEST-003**: Test admin-only access to archive section
- **TEST-004**: Test restore functionality maintains original folder/project structure
- **TEST-005**: Test automatic cleanup after 1-week retention period
- **TEST-006**: Test empty archive functionality removes all archived items
- **TEST-007**: Verify non-admin users cannot access archive operations

## 7. Risks & Assumptions

- **RISK-001**: Data loss if restore functionality fails - Mitigated by comprehensive testing
- **RISK-002**: Performance impact from cascade operations - Mitigated by efficient queries
- **RISK-003**: UI complexity in admin section - Mitigated by following existing patterns
- **ASSUMPTION-001**: Admin users are trusted to manage archive operations
- **ASSUMPTION-002**: 1-week retention period is sufficient for recovery needs
- **ASSUMPTION-003**: Existing RLS policies can be extended for archive functionality

## 8. Related Specifications / Further Reading

- [New Feature Implementation Guidelines](.augment/rules/new feature.md)
- [Database Schema Documentation](docs/database-schema.md)
- [Multi-User Collaboration Guide](docs/multi-user-collaboration-guide.md)
