/**
 * RecurrencePatternSelector - Component for selecting and configuring recurrence patterns
 * Provides intuitive controls for different recurrence types (daily, weekly, monthly, yearly, custom)
 */

import React from 'react';
import { 
  RecurrenceFrequency, 
  RecurrencePatternConfig,
  DailyPattern,
  WeeklyPattern,
  MonthlyPattern,
  YearlyPattern
} from '../types/recurrence';

interface RecurrencePatternSelectorProps {
  frequency: RecurrenceFrequency;
  patternConfig: RecurrencePatternConfig;
  onFrequencyChange: (frequency: RecurrenceFrequency) => void;
  onPatternConfigChange: (config: RecurrencePatternConfig) => void;
  validationErrors: Record<string, string>;
}

export default function RecurrencePatternSelector({
  frequency,
  patternConfig,
  onFrequencyChange,
  onPatternConfigChange,
  validationErrors
}: RecurrencePatternSelectorProps) {
  
  const handleFrequencyChange = (newFrequency: RecurrenceFrequency) => {
    onFrequencyChange(newFrequency);
  };

  const updatePatternConfig = (updates: Partial<RecurrencePatternConfig>) => {
    onPatternConfigChange({ ...patternConfig, ...updates });
  };

  return (
    <div className="space-y-4">
      {/* Frequency Selector */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Frequency *
        </label>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {(['daily', 'weekly', 'monthly', 'yearly', 'custom'] as RecurrenceFrequency[]).map((freq) => (
            <button
              key={freq}
              type="button"
              onClick={() => handleFrequencyChange(freq)}
              className={`px-3 py-2 text-sm font-medium rounded-lg border transition-colors ${
                frequency === freq
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              {freq.charAt(0).toUpperCase() + freq.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Pattern Configuration */}
      <div className="border border-gray-200 rounded-lg p-4">
        {frequency === 'daily' && (
          <DailyPatternConfig
            pattern={patternConfig as DailyPattern}
            onChange={updatePatternConfig}
            validationErrors={validationErrors}
          />
        )}
        
        {frequency === 'weekly' && (
          <WeeklyPatternConfig
            pattern={patternConfig as WeeklyPattern}
            onChange={updatePatternConfig}
            validationErrors={validationErrors}
          />
        )}
        
        {frequency === 'monthly' && (
          <MonthlyPatternConfig
            pattern={patternConfig as MonthlyPattern}
            onChange={updatePatternConfig}
            validationErrors={validationErrors}
          />
        )}
        
        {frequency === 'yearly' && (
          <YearlyPatternConfig
            pattern={patternConfig as YearlyPattern}
            onChange={updatePatternConfig}
            validationErrors={validationErrors}
          />
        )}
        
        {frequency === 'custom' && (
          <CustomPatternConfig
            pattern={patternConfig}
            onChange={updatePatternConfig}
            validationErrors={validationErrors}
          />
        )}
      </div>
    </div>
  );
}

// Daily Pattern Configuration
function DailyPatternConfig({ 
  pattern, 
  onChange, 
  validationErrors 
}: { 
  pattern: DailyPattern; 
  onChange: (updates: Partial<DailyPattern>) => void;
  validationErrors: Record<string, string>;
}) {
  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Daily Recurrence</h4>
      
      <div className="flex items-center gap-4">
        <label className="text-sm text-gray-700">Every</label>
        <input
          type="number"
          min="1"
          max="365"
          value={pattern.interval || 1}
          onChange={(e) => onChange({ interval: parseInt(e.target.value) || 1 })}
          className="w-20 px-2 py-1 border border-gray-300 rounded text-center"
        />
        <label className="text-sm text-gray-700">day(s)</label>
      </div>

      <div className="flex items-center gap-2">
        <input
          type="checkbox"
          id="weekdaysOnly"
          checked={pattern.weekdaysOnly || false}
          onChange={(e) => onChange({ weekdaysOnly: e.target.checked })}
          className="rounded border-gray-300"
        />
        <label htmlFor="weekdaysOnly" className="text-sm text-gray-700">
          Weekdays only (skip weekends)
        </label>
      </div>
    </div>
  );
}

// Weekly Pattern Configuration
function WeeklyPatternConfig({ 
  pattern, 
  onChange, 
  validationErrors 
}: { 
  pattern: WeeklyPattern; 
  onChange: (updates: Partial<WeeklyPattern>) => void;
  validationErrors: Record<string, string>;
}) {
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  const toggleDay = (dayIndex: number) => {
    const currentDays = pattern.daysOfWeek || [];
    const newDays = currentDays.includes(dayIndex)
      ? currentDays.filter(d => d !== dayIndex)
      : [...currentDays, dayIndex].sort();
    onChange({ daysOfWeek: newDays });
  };

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Weekly Recurrence</h4>
      
      <div className="flex items-center gap-4">
        <label className="text-sm text-gray-700">Every</label>
        <input
          type="number"
          min="1"
          max="52"
          value={pattern.interval || 1}
          onChange={(e) => onChange({ interval: parseInt(e.target.value) || 1 })}
          className="w-20 px-2 py-1 border border-gray-300 rounded text-center"
        />
        <label className="text-sm text-gray-700">week(s) on:</label>
      </div>

      <div className="grid grid-cols-7 gap-2">
        {dayNames.map((day, index) => (
          <button
            key={index}
            type="button"
            onClick={() => toggleDay(index)}
            className={`px-2 py-2 text-sm font-medium rounded border transition-colors ${
              (pattern.daysOfWeek || []).includes(index)
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            }`}
          >
            {day}
          </button>
        ))}
      </div>
      
      {validationErrors.daysOfWeek && (
        <p className="text-sm text-red-600">{validationErrors.daysOfWeek}</p>
      )}
    </div>
  );
}

// Monthly Pattern Configuration
function MonthlyPatternConfig({ 
  pattern, 
  onChange, 
  validationErrors 
}: { 
  pattern: MonthlyPattern; 
  onChange: (updates: Partial<MonthlyPattern>) => void;
  validationErrors: Record<string, string>;
}) {
  const [monthlyType, setMonthlyType] = React.useState<'dayOfMonth' | 'weekOfMonth'>(
    pattern.dayOfMonth ? 'dayOfMonth' : 'weekOfMonth'
  );

  const handleTypeChange = (type: 'dayOfMonth' | 'weekOfMonth') => {
    setMonthlyType(type);
    if (type === 'dayOfMonth') {
      onChange({ dayOfMonth: new Date().getDate(), weekOfMonth: undefined, dayOfWeek: undefined });
    } else {
      onChange({ dayOfMonth: undefined, weekOfMonth: 1, dayOfWeek: new Date().getDay() });
    }
  };

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Monthly Recurrence</h4>
      
      <div className="flex items-center gap-4">
        <label className="text-sm text-gray-700">Every</label>
        <input
          type="number"
          min="1"
          max="12"
          value={pattern.interval || 1}
          onChange={(e) => onChange({ interval: parseInt(e.target.value) || 1 })}
          className="w-20 px-2 py-1 border border-gray-300 rounded text-center"
        />
        <label className="text-sm text-gray-700">month(s)</label>
      </div>

      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <input
            type="radio"
            id="dayOfMonth"
            name="monthlyType"
            checked={monthlyType === 'dayOfMonth'}
            onChange={() => handleTypeChange('dayOfMonth')}
          />
          <label htmlFor="dayOfMonth" className="text-sm text-gray-700">On day</label>
          <input
            type="number"
            min="1"
            max="31"
            value={pattern.dayOfMonth || new Date().getDate()}
            onChange={(e) => onChange({ dayOfMonth: parseInt(e.target.value) || 1 })}
            disabled={monthlyType !== 'dayOfMonth'}
            className="w-16 px-2 py-1 border border-gray-300 rounded text-center disabled:bg-gray-100"
          />
          <span className="text-sm text-gray-700">of the month</span>
        </div>

        <div className="flex items-center gap-2">
          <input
            type="radio"
            id="weekOfMonth"
            name="monthlyType"
            checked={monthlyType === 'weekOfMonth'}
            onChange={() => handleTypeChange('weekOfMonth')}
          />
          <label htmlFor="weekOfMonth" className="text-sm text-gray-700">On the</label>
          <select
            value={pattern.weekOfMonth || 1}
            onChange={(e) => onChange({ weekOfMonth: parseInt(e.target.value) })}
            disabled={monthlyType !== 'weekOfMonth'}
            className="px-2 py-1 border border-gray-300 rounded disabled:bg-gray-100"
          >
            <option value={1}>1st</option>
            <option value={2}>2nd</option>
            <option value={3}>3rd</option>
            <option value={4}>4th</option>
            <option value={-1}>Last</option>
          </select>
          <select
            value={pattern.dayOfWeek || 0}
            onChange={(e) => onChange({ dayOfWeek: parseInt(e.target.value) })}
            disabled={monthlyType !== 'weekOfMonth'}
            className="px-2 py-1 border border-gray-300 rounded disabled:bg-gray-100"
          >
            <option value={0}>Sunday</option>
            <option value={1}>Monday</option>
            <option value={2}>Tuesday</option>
            <option value={3}>Wednesday</option>
            <option value={4}>Thursday</option>
            <option value={5}>Friday</option>
            <option value={6}>Saturday</option>
          </select>
        </div>
      </div>
    </div>
  );
}

// Yearly Pattern Configuration
function YearlyPatternConfig({ 
  pattern, 
  onChange, 
  validationErrors 
}: { 
  pattern: YearlyPattern; 
  onChange: (updates: Partial<YearlyPattern>) => void;
  validationErrors: Record<string, string>;
}) {
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Yearly Recurrence</h4>
      
      <div className="flex items-center gap-4">
        <label className="text-sm text-gray-700">Every</label>
        <input
          type="number"
          min="1"
          max="10"
          value={pattern.interval || 1}
          onChange={(e) => onChange({ interval: parseInt(e.target.value) || 1 })}
          className="w-20 px-2 py-1 border border-gray-300 rounded text-center"
        />
        <label className="text-sm text-gray-700">year(s)</label>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm text-gray-700 mb-1">Month</label>
          <select
            value={pattern.month || new Date().getMonth() + 1}
            onChange={(e) => onChange({ month: parseInt(e.target.value) })}
            className="w-full px-2 py-1 border border-gray-300 rounded"
          >
            {monthNames.map((month, index) => (
              <option key={index} value={index + 1}>{month}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm text-gray-700 mb-1">Day</label>
          <input
            type="number"
            min="1"
            max="31"
            value={pattern.dayOfMonth || new Date().getDate()}
            onChange={(e) => onChange({ dayOfMonth: parseInt(e.target.value) || 1 })}
            className="w-full px-2 py-1 border border-gray-300 rounded"
          />
        </div>
      </div>
    </div>
  );
}

// Custom Pattern Configuration
function CustomPatternConfig({ 
  pattern, 
  onChange, 
  validationErrors 
}: { 
  pattern: any; 
  onChange: (updates: any) => void;
  validationErrors: Record<string, string>;
}) {
  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Custom Recurrence</h4>
      
      <div>
        <label className="block text-sm text-gray-700 mb-1">
          Cron Expression (Advanced)
        </label>
        <input
          type="text"
          value={pattern.cronExpression || ''}
          onChange={(e) => onChange({ cronExpression: e.target.value })}
          placeholder="0 9 * * 1-5 (9 AM on weekdays)"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg"
        />
        <p className="text-xs text-gray-500 mt-1">
          Format: minute hour day month dayOfWeek
        </p>
      </div>

      <div>
        <label className="block text-sm text-gray-700 mb-1">
          Description
        </label>
        <input
          type="text"
          value={pattern.customRules?.description || ''}
          onChange={(e) => onChange({ 
            customRules: { 
              ...pattern.customRules, 
              description: e.target.value 
            } 
          })}
          placeholder="Describe your custom pattern"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg"
        />
      </div>
    </div>
  );
}
