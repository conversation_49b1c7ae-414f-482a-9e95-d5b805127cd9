# Archive System Guide

This guide covers the complete archive and restore functionality for projects, folders, and tasks in the Project Management Tool.

## 🗄️ Overview

The archive system provides soft-delete functionality with restore capabilities, allowing administrators to safely remove items while maintaining the ability to recover them if needed.

### Key Features
- **Soft Delete**: Items are marked as archived, not permanently deleted
- **Cascade Operations**: Archiving a folder archives all its contents
- **Admin-Only Access**: Only administrators can view and restore archived items
- **Automatic Cleanup**: Items older than 1 week are permanently deleted
- **Original Location Tracking**: Maintains hierarchy for proper restoration
- **Filtered Operations**: Archived items are excluded from normal views and operations

## 🚀 How to Use

### Archiving Items

#### Archive a Project
1. **From Task Tree Sidebar**: Right-click on a project → Select "Delete Project"
2. **From Project Manager**: Hover over project → Click three-dot menu → Select "Delete"
3. **Confirmation**: Confirm the deletion in the popup dialog

#### Archive a Folder
1. **From Task Tree Sidebar**: Right-click on a folder → Select "Delete Folder"
2. **Confirmation**: Confirm the deletion in the popup dialog

**Note**: Archiving a folder will also archive:
- All subfolders within it (recursively)
- All projects within the folder and its subfolders
- All tasks within those projects
- All tasks directly assigned to the folder

### Viewing Archived Items

1. **Admin Access Required**: Only users with admin role can access archived items
2. **Navigate to Archive**: Go to Admin → Archive in the sidebar
3. **Browse Categories**: View archived folders, projects, and tasks in separate tabs
4. **Search and Filter**: Use the search functionality to find specific archived items

### Restoring Items

1. **Access Archive Manager**: Admin → Archive
2. **Find Item**: Locate the item you want to restore
3. **Restore**: Click the "Restore" button next to the item
4. **Confirmation**: Confirm the restoration in the popup dialog

**Restoration Behavior**:
- Items are restored to their original location
- If the original parent no longer exists, items are restored to the root level
- Restoring a folder restores all its contents recursively
- Restoring a project restores all its tasks

### Permanent Deletion

Items are automatically permanently deleted after 1 week in the archive. This process:
- Runs automatically via database functions
- Cannot be undone once executed
- Completely removes items from the database

## 🔧 Technical Implementation

### Database Schema

#### Archive Columns
All main tables include archive tracking:

```sql
-- Common archive columns
archived_at TIMESTAMPTZ        -- When the item was archived
archived_by UUID              -- Who archived the item
original_parent_id UUID       -- Original parent for restoration
```

#### Archive Functions

**archive_folder_cascade(folder_id, user_id)**
- Archives folder and all contents recursively
- Maintains original hierarchy information
- Handles nested folder structures

**archive_project_cascade(project_id, user_id)**
- Archives project and all its tasks
- Preserves original folder location

**restore_folder_cascade(folder_id)**
- Restores folder to original location
- Restores all contents recursively

**restore_project_cascade(project_id)**
- Restores project to original location
- Restores all project tasks

**cleanup_old_archives()**
- Permanently deletes items older than 1 week
- Runs automatically on schedule

### Frontend Implementation

#### Service Layer
- `archiveService.ts`: Core archive operations
- `supabaseService.ts`: Database integration with archive filtering
- Automatic exclusion of archived items from normal queries

#### UI Components
- `ArchiveManager.tsx`: Admin interface for viewing and restoring
- `TaskTreeSidebar.tsx`: Delete options in context menus
- Real-time updates after archive/restore operations

#### State Management
- Automatic data synchronization after operations
- Optimistic updates with error handling
- Real-time subscription filtering for archived items

## 🔐 Security and Permissions

### Access Control
- **Archive Operations**: Available to all authenticated users for their own items
- **View Archives**: Admin-only access to archive management interface
- **Restore Operations**: Admin-only functionality
- **Automatic Cleanup**: System-level operation, no user intervention

### Row Level Security (RLS)
- Archived items are filtered out of normal queries
- Admin users can access archived items through special queries
- Proper user isolation maintained in multi-user environments

## 🚨 Important Notes

### Data Safety
- **7-Day Window**: Items can be restored within 1 week of archiving
- **Cascade Effects**: Archiving a folder affects all its contents
- **No Undo**: Permanent deletion after 1 week cannot be undone
- **Admin Responsibility**: Only admins can restore items

### Performance Considerations
- Archive operations are optimized for large hierarchies
- Filtering archived items is handled at the database level
- Real-time updates are efficient and don't cause conflicts

### Best Practices
- **Regular Review**: Admins should regularly review archived items
- **Timely Restoration**: Restore important items within the 1-week window
- **User Communication**: Inform users about the archive system and retention policy
- **Backup Strategy**: Consider additional backups for critical data

## 🔍 Troubleshooting

### Common Issues

**Archive operation fails**
- Check user permissions
- Verify database connection
- Check for foreign key constraints

**Items not appearing in archive**
- Verify admin role assignment
- Check archive timestamp is within 1 week
- Refresh the archive view

**Restore operation fails**
- Check if original parent still exists
- Verify admin permissions
- Check for naming conflicts

**Items still visible after archiving**
- Clear browser cache
- Check for multiple browser tabs
- Verify real-time sync is working

### Debug Information
- Check browser console for error messages
- Review Supabase logs for database errors
- Verify RLS policies are correctly applied
- Test with different user roles

## 📚 Related Documentation

- [Database Schema Guide](./database-schema.md) - Complete database structure
- [Multi-User Collaboration Guide](./multi-user-collaboration-guide.md) - User roles and permissions
- [Troubleshooting Guide](./troubleshooting.md) - General troubleshooting
- [Supabase Setup Guide](../SUPABASE_SETUP.md) - Database setup instructions

## 🆘 Support

For issues with the archive system:
1. Check the troubleshooting section above
2. Verify admin permissions are correctly assigned
3. Review database logs in Supabase dashboard
4. Test with a simple archive/restore operation
5. Create an issue with detailed error information

The archive system is designed to be safe and reliable, providing peace of mind when managing project data while maintaining the flexibility to recover from accidental deletions.
