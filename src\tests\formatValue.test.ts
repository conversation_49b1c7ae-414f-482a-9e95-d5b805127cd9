import { formatValue } from '../components/formatValue';

describe('formatValue', () => {
  const mockColumns = [
    { id: 'todo', title: 'To Do' },
    { id: 'in-progress', title: 'In Progress' },
    { id: 'done', title: 'Done' }
  ];

  const mockUsers = [
    { id: 'user1', name: '<PERSON>' },
    { id: 'user2', name: '<PERSON>' }
  ];

  const mockFolders = [
    { id: 'folder1', name: 'Development', parentId: undefined },
    { id: 'folder2', name: 'Testing', parentId: 'folder1' },
    { id: 'folder3', name: 'Documentation', parentId: undefined }
  ];

  describe('folderId field handling', () => {
    it('should return folder name for valid folderId', () => {
      const result = formatValue('folderId', '"folder1"', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('Development');
    });

    it('should return "No folder" for null folderId', () => {
      const result = formatValue('folderId', 'null', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('No folder');
    });

    it('should return "No folder" for empty string folderId', () => {
      const result = formatValue('folderId', '""', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('No folder');
    });

    it('should return original value for unknown folderId', () => {
      const result = formatValue('folderId', '"unknown-folder"', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('unknown-folder');
    });

    it('should handle folderId without folders array', () => {
      const result = formatValue('folderId', '"folder1"', mockColumns, mockUsers);
      expect(result).toBe('folder1');
    });
  });

  describe('status field handling', () => {
    it('should return column title for valid status', () => {
      const result = formatValue('status', '"todo"', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('To Do');
    });

    it('should return original value for unknown status', () => {
      const result = formatValue('status', '"unknown"', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('unknown');
    });
  });

  describe('user field handling', () => {
    it('should return user name for assignedUserId', () => {
      const result = formatValue('assignedUserId', '"user1"', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('John Doe');
    });

    it('should return original value for unknown user', () => {
      const result = formatValue('assignedUserId', '"unknown-user"', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('unknown-user');
    });
  });

  describe('projectId field handling', () => {
    it('should return "No project" for null projectId', () => {
      const result = formatValue('projectId', 'null', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('No project');
    });

    it('should return "No project" for empty string projectId', () => {
      const result = formatValue('projectId', '""', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('No project');
    });

    it('should return original value for valid projectId', () => {
      const result = formatValue('projectId', '"project1"', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('project1');
    });
  });

  describe('edge cases', () => {
    it('should handle malformed JSON gracefully', () => {
      const result = formatValue('folderId', 'invalid-json', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('invalid-json');
    });

    it('should handle undefined values', () => {
      const result = formatValue('folderId', 'undefined', mockColumns, mockUsers, mockFolders);
      expect(result).toBe('No folder');
    });
  });
});
