/**
 * Navigation utilities for handling URL parameters and task sharing
 */

export interface TaskNavigationParams {
  taskId?: string;
  subtaskId?: string;
  view?: 'edit' | 'view';
}

export interface ShareableLink {
  url: string;
  taskId: string;
  subtaskId?: string;
}

/**
 * Parse URL parameters from the current location
 */
export function parseUrlParams(): TaskNavigationParams {
  const params = new URLSearchParams(window.location.search);
  const hashParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
  
  // Check both regular URL params and hash params for compatibility
  const taskId = params.get('taskId') || hashParams.get('taskId');
  const subtaskId = params.get('subtaskId') || hashParams.get('subtaskId');
  const view = (params.get('view') || hashParams.get('view') || 'edit') as 'edit' | 'view';
  
  return {
    taskId: taskId || undefined,
    subtaskId: subtaskId || undefined,
    view
  };
}

/**
 * Generate a shareable URL for a task
 */
export function generateTaskShareUrl(taskId: string, subtaskId?: string): ShareableLink {
  const baseUrl = window.location.origin + window.location.pathname;
  const params = new URLSearchParams();
  
  params.set('taskId', taskId);
  if (subtaskId) {
    params.set('subtaskId', subtaskId);
  }
  
  const url = `${baseUrl}#tasks?${params.toString()}`;
  
  return {
    url,
    taskId,
    subtaskId
  };
}

/**
 * Copy text to clipboard with fallback for older browsers
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    // Modern clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    }
    
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    const success = document.execCommand('copy');
    document.body.removeChild(textArea);
    
    return success;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}

/**
 * Navigate to tasks view with specific task parameters
 */
export function navigateToTask(taskId: string, subtaskId?: string, view: 'edit' | 'view' = 'edit'): void {
  const params = new URLSearchParams();
  params.set('taskId', taskId);
  
  if (subtaskId) {
    params.set('subtaskId', subtaskId);
  }
  
  if (view !== 'edit') {
    params.set('view', view);
  }
  
  // Update URL without page reload
  const newHash = `#tasks?${params.toString()}`;
  window.location.hash = newHash;
  
  // Trigger custom navigation event for components to listen to
  window.dispatchEvent(new CustomEvent('taskNavigation', {
    detail: { taskId, subtaskId, view }
  }));
}

/**
 * Clear task navigation parameters from URL
 */
export function clearTaskNavigation(): void {
  // Remove task-related parameters while preserving other hash content
  const currentHash = window.location.hash;
  const [hashBase] = currentHash.split('?');
  
  if (hashBase === '#tasks') {
    // If we're on tasks page, just clear parameters
    window.location.hash = '#tasks';
  } else {
    // If we're on another page, don't change the hash
    return;
  }
  
  // Trigger clear navigation event
  window.dispatchEvent(new CustomEvent('taskNavigationClear'));
}

/**
 * Check if current URL has task navigation parameters
 */
export function hasTaskNavigation(): boolean {
  const params = parseUrlParams();
  return !!params.taskId;
}

/**
 * Get user-friendly share message for task
 */
export function getShareMessage(taskTitle: string, shareUrl: string): string {
  return `Check out this task: "${taskTitle}"\n${shareUrl}`;
}

/**
 * Validate if a task ID format is valid (basic validation)
 */
export function isValidTaskId(taskId: string): boolean {
  // Basic validation - adjust based on your task ID format
  return typeof taskId === 'string' && taskId.length > 0 && taskId.trim() === taskId;
}

/**
 * Extract task navigation info from URL hash
 */
export function extractTaskInfoFromHash(hash: string): TaskNavigationParams | null {
  try {
    const [hashBase, queryString] = hash.split('?');
    
    if (hashBase !== '#tasks' || !queryString) {
      return null;
    }
    
    const params = new URLSearchParams(queryString);
    const taskId = params.get('taskId');
    
    if (!taskId || !isValidTaskId(taskId)) {
      return null;
    }
    
    return {
      taskId,
      subtaskId: params.get('subtaskId') || undefined,
      view: (params.get('view') as 'edit' | 'view') || 'edit'
    };
  } catch (error) {
    console.error('Error parsing task navigation from hash:', error);
    return null;
  }
}
