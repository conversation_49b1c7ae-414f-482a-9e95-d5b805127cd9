import React, { useState, useRef, useEffect } from 'react';
import { Calendar, ChevronDown } from 'lucide-react';

interface DateRangeFilterProps {
  startDate?: string;
  endDate?: string;
  onDateRangeChange: (start?: string, end?: string) => void;
}

type DateFilterType = 'due-before' | 'due-after' | 'due-in-range';

export default function DateRangeFilter({
  startDate,
  endDate,
  onDateRangeChange
}: DateRangeFilterProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [filterType, setFilterType] = useState<DateFilterType>(() => {
    if (startDate && endDate) return 'due-in-range';
    if (startDate) return 'due-after';
    if (endDate) return 'due-before';
    return 'due-before';
  });
  
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filterOptions = [
    { value: 'due-before' as const, label: 'Due before' },
    { value: 'due-after' as const, label: 'Due after' },
    { value: 'due-in-range' as const, label: 'Due in range' }
  ];

  const selectedOption = filterOptions.find(option => option.value === filterType);
  const hasActiveFilter = startDate || endDate;

  const handleFilterTypeChange = (newType: DateFilterType) => {
    setFilterType(newType);
    setIsDropdownOpen(false);
    
    // Clear existing dates when changing filter type
    onDateRangeChange(undefined, undefined);
  };

  const handleStartDateChange = (date: string) => {
    if (filterType === 'due-after') {
      onDateRangeChange(date || undefined, undefined);
    } else if (filterType === 'due-in-range') {
      onDateRangeChange(date || undefined, endDate);
    }
  };

  const handleEndDateChange = (date: string) => {
    if (filterType === 'due-before') {
      onDateRangeChange(undefined, date || undefined);
    } else if (filterType === 'due-in-range') {
      onDateRangeChange(startDate, date || undefined);
    }
  };

  return (
    <div className={`flex items-center justify-between px-3 py-2 text-sm border rounded-lg hover:bg-gray-50 transition-colors ${
      hasActiveFilter ? 'border-blue-300 bg-blue-50' : 'border-gray-300 bg-white'
    }`}>
      <div className="flex items-center gap-2">
        {/* Calendar Icon */}
        <Calendar className="w-4 h-4 text-gray-400" />

        {/* Filter Label */}
        <span className="font-medium text-gray-700">Due:</span>

        {/* Filter Type Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className={`flex items-center gap-1 text-sm ${
              hasActiveFilter ? 'text-blue-700' : 'text-gray-500'
            }`}
          >
            <span>{selectedOption?.label.toLowerCase()}</span>
            <ChevronDown className={`w-3 h-3 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

          {isDropdownOpen && (
            <div className="absolute top-full mt-1 left-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[120px]">
              {filterOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleFilterTypeChange(option.value)}
                  className={`w-full px-3 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${
                    filterType === option.value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Date Input(s) */}
      <div className="flex items-center gap-1 text-sm pr-2">
        {(filterType === 'due-after' || filterType === 'due-in-range') && (
          <input
            type="date"
            value={startDate || ''}
            onChange={(e) => handleStartDateChange(e.target.value)}
            className={`border-0 bg-transparent text-sm focus:outline-none w-[110px] ${
              hasActiveFilter ? 'text-blue-700' : 'text-gray-500'
            }`}
          />
        )}

        {filterType === 'due-in-range' && (
          <span className="text-gray-400 text-xs mx-1">to</span>
        )}

        {(filterType === 'due-before' || filterType === 'due-in-range') && (
          <input
            type="date"
            value={endDate || ''}
            onChange={(e) => handleEndDateChange(e.target.value)}
            className={`border-0 bg-transparent text-sm focus:outline-none w-[110px] ${
              hasActiveFilter ? 'text-blue-700' : 'text-gray-500'
            }`}
          />
        )}
      </div>
    </div>
  );
}
