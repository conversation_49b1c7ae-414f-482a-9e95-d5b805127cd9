/**
 * RecurrenceService - Core service for managing recurring task configurations
 * Provides CRUD operations with comprehensive audit logging and validation
 */

import { supabase, getCurrentUser } from '../lib/supabase';
import {
  TaskRecurrence,
  TaskRecurrenceExecution,
  CreateRecurrenceInput,
  UpdateRecurrenceInput,
  RecurrenceServiceResponse,
  RecurrenceStatistics,
  RecurrenceFilter,
  RecurrenceSearchResult,
  BulkRecurrenceOperation,
  BulkOperationResult,
  RecurrenceValidationResult
} from '../types/recurrence';

class RecurrenceService {
  /**
   * Create a new recurring task configuration
   */
  async createRecurrence(input: CreateRecurrenceInput): Promise<RecurrenceServiceResponse<TaskRecurrence>> {
    try {
      const user = await getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Validate input
      const validation = await this.validateRecurrenceInput(input);
      if (!validation.isValid) {
        return { success: false, error: 'Validation failed', validationErrors: validation.errors };
      }

      // Calculate initial next execution date
      const nextExecutionDate = await this.calculateNextExecution(
        input.patternConfig,
        input.startDate,
        input.timezone
      );

      const recurrenceData = {
        task_id: input.taskId,
        name: input.name,
        description: input.description || null,
        frequency: input.frequency,
        pattern_config: input.patternConfig,
        start_date: input.startDate,
        end_date: input.endDate || null,
        next_execution_date: nextExecutionDate,
        timezone: input.timezone || 'UTC',
        max_executions: input.maxExecutions || null,
        created_by: user.id
      };

      const { data, error } = await supabase
        .from('task_recurrences')
        .insert(recurrenceData)
        .select()
        .single();

      if (error) {
        console.error('Error creating recurrence:', error);
        return { success: false, error: 'Failed to create recurrence' };
      }

      return { success: true, data: this.mapDatabaseToRecurrence(data) };
    } catch (error) {
      console.error('Error in createRecurrence:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get recurrence by ID
   */
  async getRecurrence(id: string): Promise<RecurrenceServiceResponse<TaskRecurrence>> {
    try {
      const { data, error } = await supabase
        .from('task_recurrences')
        .select('*')
        .eq('id', id)
        .is('archived_at', null)
        .single();

      if (error) {
        return { success: false, error: 'Recurrence not found' };
      }

      return { success: true, data: this.mapDatabaseToRecurrence(data) };
    } catch (error) {
      console.error('Error in getRecurrence:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get all recurrences for a specific task
   */
  async getRecurrencesForTask(taskId: string): Promise<RecurrenceServiceResponse<TaskRecurrence[]>> {
    try {
      const { data, error } = await supabase
        .from('task_recurrences')
        .select('*')
        .eq('task_id', taskId)
        .is('archived_at', null)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error getting recurrences for task:', error);
        return { success: false, error: 'Failed to fetch recurrences' };
      }

      const recurrences = data.map(this.mapDatabaseToRecurrence);
      return { success: true, data: recurrences };
    } catch (error) {
      console.error('Error in getRecurrencesForTask:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Update an existing recurrence
   */
  async updateRecurrence(id: string, input: UpdateRecurrenceInput): Promise<RecurrenceServiceResponse<TaskRecurrence>> {
    try {
      const user = await getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Get current recurrence for validation
      const currentResult = await this.getRecurrence(id);
      if (!currentResult.success || !currentResult.data) {
        return { success: false, error: 'Recurrence not found' };
      }

      const updateData: any = {
        updated_by: user.id,
        version: currentResult.data.version + 1
      };

      // Only update provided fields
      if (input.name !== undefined) updateData.name = input.name;
      if (input.description !== undefined) updateData.description = input.description;
      if (input.frequency !== undefined) updateData.frequency = input.frequency;
      if (input.patternConfig !== undefined) updateData.pattern_config = input.patternConfig;
      if (input.startDate !== undefined) updateData.start_date = input.startDate;
      if (input.endDate !== undefined) updateData.end_date = input.endDate;
      if (input.timezone !== undefined) updateData.timezone = input.timezone;
      if (input.status !== undefined) updateData.status = input.status;
      if (input.isActive !== undefined) updateData.is_active = input.isActive;
      if (input.maxExecutions !== undefined) updateData.max_executions = input.maxExecutions;

      // Recalculate next execution if pattern changed
      if (input.patternConfig || input.startDate || input.timezone) {
        const patternConfig = input.patternConfig || currentResult.data.patternConfig;
        const startDate = input.startDate || currentResult.data.startDate;
        const timezone = input.timezone || currentResult.data.timezone;
        
        updateData.next_execution_date = await this.calculateNextExecution(
          patternConfig,
          startDate,
          timezone
        );
      }

      const { data, error } = await supabase
        .from('task_recurrences')
        .update(updateData)
        .eq('id', id)
        .eq('version', currentResult.data.version) // Optimistic locking
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return { success: false, error: 'Recurrence was modified by another user. Please refresh and try again.' };
        }
        console.error('Error updating recurrence:', error);
        return { success: false, error: 'Failed to update recurrence' };
      }

      return { success: true, data: this.mapDatabaseToRecurrence(data) };
    } catch (error) {
      console.error('Error in updateRecurrence:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Soft delete a recurrence (archive it)
   */
  async deleteRecurrence(id: string): Promise<RecurrenceServiceResponse<boolean>> {
    try {
      const user = await getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { error } = await supabase
        .from('task_recurrences')
        .update({
          archived_at: new Date().toISOString(),
          archived_by: user.id,
          is_active: false
        })
        .eq('id', id);

      if (error) {
        console.error('Error deleting recurrence:', error);
        return { success: false, error: 'Failed to delete recurrence' };
      }

      return { success: true, data: true };
    } catch (error) {
      console.error('Error in deleteRecurrence:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get recurrences ready for execution
   */
  async getRecurrencesDueForExecution(): Promise<RecurrenceServiceResponse<TaskRecurrence[]>> {
    try {
      const now = new Date().toISOString();
      
      const { data, error } = await supabase
        .from('task_recurrences')
        .select('*')
        .eq('is_active', true)
        .eq('status', 'active')
        .is('archived_at', null)
        .lte('next_execution_date', now);

      if (error) {
        console.error('Error getting due recurrences:', error);
        return { success: false, error: 'Failed to fetch due recurrences' };
      }

      const recurrences = data.map(this.mapDatabaseToRecurrence);
      return { success: true, data: recurrences };
    } catch (error) {
      console.error('Error in getRecurrencesDueForExecution:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Record execution of a recurrence
   */
  async recordExecution(
    recurrenceId: string,
    status: 'success' | 'failed' | 'skipped',
    createdTaskId?: string,
    errorMessage?: string,
    durationMs?: number
  ): Promise<RecurrenceServiceResponse<TaskRecurrenceExecution>> {
    try {
      const { data, error } = await supabase.rpc('record_recurrence_execution', {
        recurrence_uuid: recurrenceId,
        execution_status: status,
        created_task_uuid: createdTaskId || null,
        error_msg: errorMessage || null,
        duration_ms: durationMs || null
      });

      if (error) {
        console.error('Error recording execution:', error);
        return { success: false, error: 'Failed to record execution' };
      }

      // Get the created execution record
      const { data: executionData, error: fetchError } = await supabase
        .from('task_recurrence_executions')
        .select('*')
        .eq('id', data)
        .single();

      if (fetchError) {
        console.error('Error fetching execution record:', fetchError);
        return { success: false, error: 'Failed to fetch execution record' };
      }

      return { success: true, data: this.mapDatabaseToExecution(executionData) };
    } catch (error) {
      console.error('Error in recordExecution:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Calculate next execution date based on pattern
   */
  private async calculateNextExecution(
    patternConfig: any,
    startDate: string,
    timezone: string = 'UTC'
  ): Promise<string> {
    // This is a simplified implementation
    // In a real implementation, you'd use a proper date library like date-fns or moment
    const start = new Date(startDate);
    const now = new Date();
    
    // If start date is in the future, use it
    if (start > now) {
      return start.toISOString();
    }

    // Simple calculation based on frequency
    switch (patternConfig.type) {
      case 'daily':
        const nextDaily = new Date(now);
        nextDaily.setDate(nextDaily.getDate() + (patternConfig.interval || 1));
        return nextDaily.toISOString();
        
      case 'weekly':
        const nextWeekly = new Date(now);
        nextWeekly.setDate(nextWeekly.getDate() + (7 * (patternConfig.interval || 1)));
        return nextWeekly.toISOString();
        
      case 'monthly':
        const nextMonthly = new Date(now);
        nextMonthly.setMonth(nextMonthly.getMonth() + (patternConfig.interval || 1));
        return nextMonthly.toISOString();
        
      default:
        // Default to daily
        const nextDefault = new Date(now);
        nextDefault.setDate(nextDefault.getDate() + 1);
        return nextDefault.toISOString();
    }
  }

  /**
   * Validate recurrence input
   */
  private async validateRecurrenceInput(input: CreateRecurrenceInput): Promise<RecurrenceValidationResult> {
    const errors: Record<string, string> = {};
    const warnings: Record<string, string> = {};

    // Basic validation
    if (!input.name?.trim()) {
      errors.name = 'Name is required';
    }

    if (!input.taskId) {
      errors.taskId = 'Task ID is required';
    }

    if (!input.startDate) {
      errors.startDate = 'Start date is required';
    } else {
      const startDate = new Date(input.startDate);
      if (isNaN(startDate.getTime())) {
        errors.startDate = 'Invalid start date';
      }
    }

    if (input.endDate) {
      const endDate = new Date(input.endDate);
      const startDate = new Date(input.startDate);
      if (isNaN(endDate.getTime())) {
        errors.endDate = 'Invalid end date';
      } else if (endDate <= startDate) {
        errors.endDate = 'End date must be after start date';
      }
    }

    // Pattern-specific validation
    if (!input.patternConfig) {
      errors.patternConfig = 'Pattern configuration is required';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      warnings
    };
  }

  /**
   * Map database record to TaskRecurrence interface
   */
  private mapDatabaseToRecurrence(data: any): TaskRecurrence {
    return {
      id: data.id,
      taskId: data.task_id,
      name: data.name,
      description: data.description,
      frequency: data.frequency,
      patternConfig: data.pattern_config,
      startDate: data.start_date,
      endDate: data.end_date,
      nextExecutionDate: data.next_execution_date,
      timezone: data.timezone,
      status: data.status,
      isActive: data.is_active,
      maxExecutions: data.max_executions,
      totalExecutions: data.total_executions,
      successfulExecutions: data.successful_executions,
      failedExecutions: data.failed_executions,
      lastExecutionDate: data.last_execution_date,
      lastExecutionStatus: data.last_execution_status,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      createdBy: data.created_by,
      updatedBy: data.updated_by,
      archivedAt: data.archived_at,
      archivedBy: data.archived_by,
      version: data.version
    };
  }

  /**
   * Map database record to TaskRecurrenceExecution interface
   */
  private mapDatabaseToExecution(data: any): TaskRecurrenceExecution {
    return {
      id: data.id,
      recurrenceId: data.recurrence_id,
      scheduledDate: data.scheduled_date,
      executedDate: data.executed_date,
      status: data.status,
      createdTaskId: data.created_task_id,
      errorMessage: data.error_message,
      errorDetails: data.error_details,
      executionDurationMs: data.execution_duration_ms,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  }
}

export const recurrenceService = new RecurrenceService();
