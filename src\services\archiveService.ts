import { supabase, getCurrentUser, handleSupabaseError } from '../lib/supabase';

export interface ArchivedItem {
  id: string;
  name: string;
  type: 'folder' | 'project' | 'task';
  archivedAt: string;
  archivedBy: string;
  originalLocation?: string;
  itemCount?: number; // For folders/projects, count of contained items
}

export interface ArchiveStats {
  totalItems: number;
  folders: number;
  projects: number;
  tasks: number;
  oldestItem?: string;
}

export const archiveService = {
  /**
   * Archive a folder and all its contents (cascade)
   */
  async archiveFolder(folderId: string): Promise<void> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { error } = await supabase.rpc('archive_folder_cascade', {
      p_folder_id: folderId,
      p_archived_by_user: user.id
    });

    if (error) handleSupabaseError(error);
  },

  /**
   * Archive a project and all its tasks (cascade)
   */
  async archiveProject(projectId: string): Promise<void> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { error } = await supabase.rpc('archive_project_cascade', {
      p_project_id: projectId,
      p_archived_by_user: user.id
    });

    if (error) handleSupabaseError(error);
  },

  /**
   * Archive a single task
   */
  async archiveTask(taskId: string): Promise<void> {
    const user = await getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    // Get task details for original location tracking
    const { data: task, error: fetchError } = await supabase
      .from('tasks')
      .select('project_id, folder_id')
      .eq('id', taskId)
      .single();

    if (fetchError) handleSupabaseError(fetchError);

    const { error } = await supabase
      .from('tasks')
      .update({
        archived_at: new Date().toISOString(),
        archived_by: user.id,
        original_project_id: task.project_id,
        original_folder_id: task.folder_id
      })
      .eq('id', taskId);

    if (error) handleSupabaseError(error);
  },

  /**
   * Get all archived items (admin only)
   */
  async getArchivedItems(): Promise<ArchivedItem[]> {
    const items: ArchivedItem[] = [];

    // Get archived folders
    const { data: folders, error: foldersError } = await supabase
      .from('folders')
      .select(`
        id,
        name,
        archived_at,
        archived_by,
        original_parent_id
      `)
      .not('archived_at', 'is', null)
      .order('archived_at', { ascending: false });

    if (foldersError) handleSupabaseError(foldersError);

    // Get archived projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select(`
        id,
        name,
        archived_at,
        archived_by,
        original_folder_id
      `)
      .not('archived_at', 'is', null)
      .order('archived_at', { ascending: false });

    if (projectsError) handleSupabaseError(projectsError);

    // Get archived tasks
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        archived_at,
        archived_by,
        original_project_id,
        original_folder_id
      `)
      .not('archived_at', 'is', null)
      .order('archived_at', { ascending: false });

    if (tasksError) handleSupabaseError(tasksError);

    // Get user names for archived_by IDs
    const allArchivedByIds = [
      ...(folders || []).map(f => f.archived_by),
      ...(projects || []).map(p => p.archived_by),
      ...(tasks || []).map(t => t.archived_by)
    ].filter(Boolean);

    const uniqueUserIds = [...new Set(allArchivedByIds)];

    let userNames: Record<string, string> = {};
    if (uniqueUserIds.length > 0) {
      const { data: userProfiles, error: usersError } = await supabase
        .from('user_profiles')
        .select('id, name')
        .in('id', uniqueUserIds);

      if (!usersError && userProfiles) {
        userNames = userProfiles.reduce((acc, user) => {
          acc[user.id] = user.name;
          return acc;
        }, {} as Record<string, string>);
      }
    }

    // Transform folders
    if (folders) {
      for (const folder of folders) {
        items.push({
          id: folder.id,
          name: folder.name,
          type: 'folder',
          archivedAt: folder.archived_at,
          archivedBy: userNames[folder.archived_by] || 'Unknown',
          originalLocation: folder.original_parent_id ? 'Subfolder' : 'Root'
        });
      }
    }

    // Transform projects
    if (projects) {
      for (const project of projects) {
        items.push({
          id: project.id,
          name: project.name,
          type: 'project',
          archivedAt: project.archived_at,
          archivedBy: userNames[project.archived_by] || 'Unknown',
          originalLocation: project.original_folder_id ? 'In Folder' : 'Root'
        });
      }
    }

    // Transform tasks
    if (tasks) {
      for (const task of tasks) {
        let originalLocation = 'Root';
        if (task.original_project_id) {
          originalLocation = 'In Project';
        } else if (task.original_folder_id) {
          originalLocation = 'In Folder';
        }

        items.push({
          id: task.id,
          name: task.title,
          type: 'task',
          archivedAt: task.archived_at,
          archivedBy: userNames[task.archived_by] || 'Unknown',
          originalLocation
        });
      }
    }

    return items.sort((a, b) => new Date(b.archivedAt).getTime() - new Date(a.archivedAt).getTime());
  },

  /**
   * Get archive statistics (admin only)
   */
  async getArchiveStats(): Promise<ArchiveStats> {
    const { data: stats, error } = await supabase
      .from('folders')
      .select('archived_at')
      .not('archived_at', 'is', null);

    if (error) handleSupabaseError(error);

    const { data: projectStats, error: projectError } = await supabase
      .from('projects')
      .select('archived_at')
      .not('archived_at', 'is', null);

    if (projectError) handleSupabaseError(projectError);

    const { data: taskStats, error: taskError } = await supabase
      .from('tasks')
      .select('archived_at')
      .not('archived_at', 'is', null);

    if (taskError) handleSupabaseError(taskError);

    const folders = stats?.length || 0;
    const projects = projectStats?.length || 0;
    const tasks = taskStats?.length || 0;

    // Find oldest archived item
    const allDates = [
      ...(stats || []).map(s => s.archived_at),
      ...(projectStats || []).map(s => s.archived_at),
      ...(taskStats || []).map(s => s.archived_at)
    ].filter(Boolean);

    const oldestItem = allDates.length > 0 
      ? new Date(Math.min(...allDates.map(d => new Date(d).getTime()))).toISOString()
      : undefined;

    return {
      totalItems: folders + projects + tasks,
      folders,
      projects,
      tasks,
      oldestItem
    };
  },

  /**
   * Restore a folder and all its contents
   */
  async restoreFolder(folderId: string): Promise<void> {
    const { error } = await supabase.rpc('restore_folder_cascade', {
      p_folder_id: folderId
    });

    if (error) handleSupabaseError(error);
  },

  /**
   * Restore a project and all its tasks
   */
  async restoreProject(projectId: string): Promise<void> {
    const { error } = await supabase.rpc('restore_project_cascade', {
      p_project_id: projectId
    });

    if (error) handleSupabaseError(error);
  },

  /**
   * Restore a single task
   */
  async restoreTask(taskId: string): Promise<void> {
    // Get the task's original location first
    const { data: task, error: fetchError } = await supabase
      .from('tasks')
      .select('original_project_id, original_folder_id')
      .eq('id', taskId)
      .single();

    if (fetchError) handleSupabaseError(fetchError);

    const { error } = await supabase
      .from('tasks')
      .update({
        archived_at: null,
        archived_by: null,
        project_id: task.original_project_id,
        folder_id: task.original_folder_id,
        original_project_id: null,
        original_folder_id: null
      })
      .eq('id', taskId);

    if (error) handleSupabaseError(error);
  },

  /**
   * Empty the entire archive (permanently delete all archived items)
   */
  async emptyArchive(): Promise<number> {
    // Delete all archived items permanently
    const { data: deletedCount, error } = await supabase.rpc('cleanup_expired_archives');

    if (error) handleSupabaseError(error);

    // Also delete items that aren't expired yet (force empty)
    const { error: tasksError } = await supabase
      .from('tasks')
      .delete()
      .not('archived_at', 'is', null);

    if (tasksError) handleSupabaseError(tasksError);

    const { error: projectsError } = await supabase
      .from('projects')
      .delete()
      .not('archived_at', 'is', null);

    if (projectsError) handleSupabaseError(projectsError);

    const { error: foldersError } = await supabase
      .from('folders')
      .delete()
      .not('archived_at', 'is', null);

    if (foldersError) handleSupabaseError(foldersError);

    return deletedCount || 0;
  },

  /**
   * Run automatic cleanup of expired archives (called by scheduled job)
   */
  async cleanupExpiredArchives(): Promise<number> {
    const { data: deletedCount, error } = await supabase.rpc('cleanup_expired_archives');

    if (error) handleSupabaseError(error);

    return deletedCount || 0;
  }
};
