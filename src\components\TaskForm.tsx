import React, { useState, useCallback, useMemo } from 'react';
import { Task, TaskEffort } from '../types';
import { ChevronDown, ChevronRight, Folder, MoreVertical, Copy, Share2 } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import TaskHistory from './TaskHistory';
import TaskComments from './TaskComments';
import CompactTimelineStats from './CompactTimelineStats';
import SubtaskList from './SubtaskList';
import TaskEffortEstimator from './TaskEffortEstimator';
import TaskDependencyManager from './TaskDependencyManager';
import CustomFieldsSection from './CustomFieldsSection';
import ClickableStatusBadge from './ClickableStatusBadge';
import ClickablePriorityBadge from './ClickablePriorityBadge';
import ClickableProjectBadge from './ClickableProjectBadge';
import CompactAssignmentField from './CompactAssignmentField';
import RichTextEditor from './RichTextEditor';

interface TaskFormProps {
  onSubmit: (task: Omit<Task, 'id'>) => void;
  onClose: () => void;
  initialData?: Task;
}

export default function TaskForm({ onSubmit, onClose, initialData }: TaskFormProps) {
  const { userGroups, projects, users, folders, tasks, columns, cloneTask } = useSupabaseStore();

  // State for tracking dependency form
  const [isDependencyFormOpen, setIsDependencyFormOpen] = useState(false);

  // State for three-dot menu
  const [showActionsMenu, setShowActionsMenu] = useState(false);

  // Get the current task from the store to ensure we have the latest data
  // If not found in store, fall back to initialData (for newly created tasks)
  // Use useMemo to prevent unnecessary re-renders when dependency form is open
  const currentTask = useMemo(() => {
    if (!initialData) return null;

    // If dependency form is open, use a stable version to prevent re-renders
    if (isDependencyFormOpen) {
      return initialData;
    }

    return tasks.find(t => t.id === initialData.id) || initialData;
  }, [initialData, tasks, isDependencyFormOpen]);

  // Debug logging
  if (initialData && !tasks.find(t => t.id === initialData.id)) {
    console.log('TaskForm: Task not found in store, using initialData fallback', {
      taskId: initialData.id,
      taskTitle: initialData.title,
      storeTaskCount: tasks.length
    });
  }

  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    description: initialData?.description || '',
    status: initialData?.status || 'todo' as Task['status'],
    priority: initialData?.priority || 'medium' as Task['priority'],
    startDate: initialData?.startDate || '',
    dueDate: initialData?.dueDate || '',
    tags: initialData?.tags || [] as string[],
    assignedGroups: initialData?.assignedGroups || [] as string[],
    assignedUsers: initialData?.assignedUsers || [] as string[],
    assignedUserId: initialData?.assignedUserId || '',
    ownerId: initialData?.ownerId || '',
    projectId: initialData?.projectId || '',
    folderId: initialData?.folderId || '',
    subtasks: initialData?.subtasks || [],
    comments: initialData?.comments || [],
    history: initialData?.history || [],
    durations: initialData?.durations || [],
    effort: initialData?.effort || undefined,
  });

  const [taskUpdated, setTaskUpdated] = useState(false);
  const [isAdvancedExpanded, setIsAdvancedExpanded] = useState(false);
  const [isEffortExpanded, setIsEffortExpanded] = useState(false);
  const [isDependencyExpanded, setIsDependencyExpanded] = useState(false);
  const [isHistoryExpanded, setIsHistoryExpanded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubtaskFormOpen, setIsSubtaskFormOpen] = useState(false);

  // Custom fields state
  const [customFieldValues, setCustomFieldValues] = useState<Record<string, any>>(
    initialData?.customFieldValues || {}
  );
  const [customFieldErrors, setCustomFieldErrors] = useState<Record<string, string>>({});
  const [isCustomFieldsExpanded, setIsCustomFieldsExpanded] = useState(false);

  // Get folder breadcrumb path
  const getFolderPath = (folderId?: string): string => {
    if (!folderId) return '';
    
    const folder = folders.find(f => f.id === folderId);
    if (!folder) return '';
    
    if (folder.parentId) {
      return getFolderPath(folder.parentId) + ' / ' + folder.name;
    }
    return folder.name;
  };

  // Get task location for display
  const getTaskLocation = (): string => {
    const parts = [];
    
    if (formData.projectId) {
      const project = projects.find(p => p.id === formData.projectId);
      if (project) {
        if (project.folderId) {
          parts.push(getFolderPath(project.folderId));
        }
        parts.push(project.name);
      }
    } else if (formData.folderId) {
      parts.push(getFolderPath(formData.folderId));
    }
    
    return parts.join(' / ');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return; // Prevent double submissions
    
    setIsSubmitting(true);
    try {
      onSubmit({
        ...formData,
        customFieldValues,
        comments: initialData?.comments || [],
        history: initialData?.history || [],
        durations: initialData?.durations || []
      });
    } finally {
      // Reset after a short delay to allow the form to close
      setTimeout(() => setIsSubmitting(false), 1000);
    }
  };

  const handleSubtaskUpdate = () => {
    setTaskUpdated(!taskUpdated);
  };

  const handleEffortChange = useCallback((effort: TaskEffort) => {
    setFormData(prev => ({ ...prev, effort }));
  }, []);

  const handleCloneTask = async () => {
    if (!initialData?.id) return;

    try {
      await cloneTask(initialData.id);
      setShowActionsMenu(false);
      onClose(); // Close the form after cloning
    } catch (error) {
      console.error('Failed to clone task:', error);
      alert('Failed to clone task. Please try again.');
    }
  };

  const handleShareTask = async () => {
    if (!initialData?.id) return;

    try {
      const { generateTaskShareUrl, copyToClipboard } = await import('../utils/navigationUtils');

      const shareLink = generateTaskShareUrl(initialData.id);

      const success = await copyToClipboard(shareLink.url);

      if (success) {
        alert('Task link copied to clipboard!');
      } else {
        // Fallback: show the link in a prompt
        prompt('Copy this link to share the task:', shareLink.url);
      }

      setShowActionsMenu(false);
    } catch (error) {
      console.error('Failed to share task:', error);
      alert('Failed to generate share link. Please try again.');
    }
  };

  // Handle dropdown toggles
  const handleUserToggle = (userId: string) => {
    const newUsers = formData.assignedUsers.includes(userId)
      ? formData.assignedUsers.filter(id => id !== userId)
      : [...formData.assignedUsers, userId];
    setFormData({ ...formData, assignedUsers: newUsers });
  };

  const handleGroupToggle = (groupId: string) => {
    const newGroups = formData.assignedGroups.includes(groupId)
      ? formData.assignedGroups.filter(id => id !== groupId)
      : [...formData.assignedGroups, groupId];
    setFormData({ ...formData, assignedGroups: newGroups });
  };

  return (
    <div
      className={`fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-4 overflow-auto ${
        isSubtaskFormOpen ? 'z-40' : 'z-50'
      }`}
      onClick={() => {
        setShowActionsMenu(false);
        onClose();
      }}
    >
      <div
        className={`bg-white rounded-xl shadow-2xl w-full max-w-[1400px] mb-8 mx-4 transition-all duration-300 ease-in-out ${
          isSubtaskFormOpen ? 'scale-90 opacity-70 blur-sm pointer-events-none' : 'scale-100 opacity-100 blur-0 pointer-events-auto'
        }`}
        onClick={(e) => {
          e.stopPropagation();
          setShowActionsMenu(false);
        }}
      >
        {/* Sticky Header */}
        <div className="sticky top-0 bg-white rounded-t-xl border-b border-gray-200 p-6 z-10">
          <div className="flex items-center justify-between gap-4">
            {/* Left side: Title, Status, Priority */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <input
                type="text"
                required
                placeholder={initialData ? "Task title..." : "Enter task title..."}
                className="text-xl font-semibold border-none outline-none bg-transparent placeholder-gray-400 flex-1 min-w-0"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                title={formData.title} // Show full title on hover
              />
              <ClickableProjectBadge
                projectId={formData.projectId}
                projects={projects}
                onChange={(projectId) => setFormData({ ...formData, projectId })}
              />
              <ClickableStatusBadge
                status={formData.status}
                columns={columns}
                onChange={(status) => setFormData({ ...formData, status })}
              />
              <ClickablePriorityBadge
                priority={formData.priority}
                onChange={(priority) => setFormData({ ...formData, priority })}
              />
            </div>

            {/* Right side: Action buttons */}
            <div className="flex items-center gap-2 flex-shrink-0">
              {/* Three-dot menu for existing tasks */}
              {initialData && (
                <div className="relative">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowActionsMenu(!showActionsMenu);
                    }}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="More actions"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </button>

                  {showActionsMenu && (
                    <div
                      className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-50 min-w-[140px]"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <button
                        type="button"
                        onClick={handleShareTask}
                        className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <Share2 className="w-4 h-4" />
                        Share Task
                      </button>
                      <button
                        type="button"
                        onClick={handleCloneTask}
                        className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <Copy className="w-4 h-4" />
                        Clone Task
                      </button>
                    </div>
                  )}
                </div>
              )}

              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors font-medium text-sm"
              >
                Cancel
              </button>
              <button
                type="submit"
                form="task-form"
                disabled={isSubmitting}
                className={`px-4 py-2 text-white rounded-lg transition-colors font-medium text-sm shadow-sm ${
                  isSubmitting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isSubmitting
                  ? 'Saving...'
                  : (initialData ? 'Update Task' : 'Create Task')
                }
              </button>
            </div>
          </div>

          {/* Location indicator */}
          {(formData.projectId || formData.folderId) && (
            <div className="flex items-center gap-2 text-sm text-gray-500 mt-3">
              <Folder className="w-4 h-4" />
              <span>{getTaskLocation()}</span>
            </div>
          )}

          {/* Compact Timeline Stats */}
          {currentTask && (
            <div className="mt-4">
              <CompactTimelineStats
                durations={currentTask.durations}
                currentStatus={currentTask.status}
              />
            </div>
          )}
        </div>

        {/* Scrollable Content */}
        <div className="p-6 pt-0">

          <div className="space-y-6">
            {/* Main Form Section - Full Width */}
            <form id="task-form" onSubmit={handleSubmit} className="space-y-6">
              {/* Main Properties - Compact 4-Column Layout */}
              <div className="bg-gray-50 p-5 rounded-xl border border-gray-200">
                <div className="grid grid-cols-4 gap-4">
                  {/* Dates Section */}
                  <div>
                    <label className="block text-sm font-medium mb-1">Start Date</label>
                    <input
                      type="date"
                      className="w-full border rounded-lg p-2 text-sm"
                      value={formData.startDate}
                      onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Due Date</label>
                    <input
                      type="date"
                      className="w-full border rounded-lg p-2 text-sm"
                      value={formData.dueDate}
                      onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                    />
                  </div>

                  {/* Assignment Section - Light Blue Background */}
                  <div className="bg-blue-50 p-3 rounded-lg border border-blue-200 -m-3 col-span-2">
                    <div className="grid grid-cols-2 gap-4">
                      <CompactAssignmentField
                        label="Primary Assignee"
                        selectedUserId={formData.assignedUserId}
                        users={users}
                        additionalUsers={formData.assignedUsers}
                        onUserChange={(userId) => setFormData({ ...formData, assignedUserId: userId })}
                        onAdditionalUsersChange={(userIds) => setFormData({ ...formData, assignedUsers: userIds })}
                        showAdvanced={true}
                      />

                      <CompactAssignmentField
                        label="Owner"
                        selectedUserId={formData.ownerId}
                        users={users}
                        groups={formData.assignedGroups}
                        availableGroups={userGroups}
                        onUserChange={(userId) => setFormData({ ...formData, ownerId: userId })}
                        onGroupsChange={(groupIds) => setFormData({ ...formData, assignedGroups: groupIds })}
                        showAdvanced={true}
                      />
                    </div>
                  </div>
                </div>
              </div>



              {/* Description */}
              <div>
                <label className="block text-sm font-medium mb-1">📝 Description</label>
                <RichTextEditor
                  value={formData.description}
                  onChange={(value) => setFormData({ ...formData, description: value })}
                  placeholder="Enter task description..."
                />
              </div>

              {/* Advanced Settings - Collapsible */}
              <div className="border rounded-lg bg-gray-50">
                <button
                  type="button"
                  onClick={() => setIsAdvancedExpanded(!isAdvancedExpanded)}
                  className="flex items-center gap-2 w-full text-left p-3 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  {isAdvancedExpanded ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                  <span className="font-medium text-gray-700">⚙️ Advanced Settings</span>
                  <span className="text-xs text-gray-500 ml-auto">
                    Effort, Custom Fields, Dependencies
                  </span>
                </button>

                {isAdvancedExpanded && (
                  <div className="border-t bg-white rounded-b-lg">
                    <div className="p-3 space-y-3">
                      {/* Effort Estimation */}
                      <div className="border rounded border-gray-200">
                        <button
                          type="button"
                          onClick={() => setIsEffortExpanded(!isEffortExpanded)}
                          className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-50 rounded transition-colors"
                        >
                          {isEffortExpanded ? (
                            <ChevronDown className="w-3 h-3" />
                          ) : (
                            <ChevronRight className="w-3 h-3" />
                          )}
                          <span className="text-sm font-medium">Effort Estimation</span>
                          {formData.effort && (
                            <span className="ml-auto text-xs text-gray-500">
                              {formData.effort.estimatedHours}h estimated
                            </span>
                          )}
                        </button>

                        {isEffortExpanded && (
                          <div className="border-t p-2">
                            <TaskEffortEstimator
                              taskId={currentTask?.id}
                              initialEffort={formData.effort}
                              onEffortChange={handleEffortChange}
                              durations={currentTask?.durations || []}
                              currentStatus={formData.status}
                            />
                          </div>
                        )}
                      </div>

                      {/* Custom Fields */}
                      <div className="border rounded border-gray-200">
                        <button
                          type="button"
                          onClick={() => setIsCustomFieldsExpanded(!isCustomFieldsExpanded)}
                          className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-50 rounded transition-colors"
                        >
                          {isCustomFieldsExpanded ? (
                            <ChevronDown className="w-3 h-3" />
                          ) : (
                            <ChevronRight className="w-3 h-3" />
                          )}
                          <span className="text-sm font-medium text-purple-600">Custom Fields</span>
                          <span className="text-xs text-gray-500 ml-auto">
                            Available fields
                          </span>
                        </button>

                        {isCustomFieldsExpanded && (
                          <div className="border-t p-2">
                            <CustomFieldsSection
                              values={customFieldValues}
                              onChange={setCustomFieldValues}
                              errors={customFieldErrors}
                            />
                          </div>
                        )}
                      </div>

                      {/* Dependencies (only for existing tasks) */}
                      {currentTask && (
                        <div className="border rounded border-gray-200">
                          <button
                            type="button"
                            onClick={() => setIsDependencyExpanded(!isDependencyExpanded)}
                            className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-50 rounded transition-colors"
                          >
                            {isDependencyExpanded ? (
                              <ChevronDown className="w-3 h-3" />
                            ) : (
                              <ChevronRight className="w-3 h-3" />
                            )}
                            <span className="text-sm font-medium">Dependencies</span>
                            <span className="text-xs text-gray-500 ml-auto">
                              Manage dependencies
                            </span>
                          </button>

                          {isDependencyExpanded && (
                            <div className="border-t p-2">
                              <TaskDependencyManager
                                key={`dependency-manager-${currentTask.id}`}
                                task={currentTask}
                                onTaskUpdate={(updatedTask) => {
                                  // Handle task updates if needed
                                  console.log('Task updated:', updatedTask);
                                }}
                                onDependencyFormStateChange={setIsDependencyFormOpen}
                              />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

            </form>
          </div>

          {/* Subtasks Section */}
          {currentTask && (
            <div className="mt-6">
              <SubtaskList
                taskId={currentTask.id}
                subtasks={currentTask.subtasks}
                parentTaskStatus={formData.status}
                onSubtaskUpdate={handleSubtaskUpdate}
                onSubtaskFormStateChange={setIsSubtaskFormOpen}
              />
            </div>
          )}

          {/* Comments Section - Full Width */}
          {currentTask && (
            <div className="mt-6">
              <TaskComments taskId={currentTask.id} comments={currentTask.comments} />
            </div>
          )}

          {/* History Section - Full Width Expandable */}
          {currentTask && (
            <div className="mt-6">
              <div className="bg-gray-50 rounded-xl border border-gray-200 overflow-hidden">
                <button
                  type="button"
                  onClick={() => setIsHistoryExpanded(!isHistoryExpanded)}
                  className="flex items-center justify-between w-full text-left p-4 hover:bg-gray-100 transition-colors font-medium"
                >
                  <div className="flex items-center gap-2">
                    {isHistoryExpanded ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                    <span>Task History</span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {currentTask.history?.length || 0} entries
                  </span>
                </button>

                {isHistoryExpanded && (
                  <div className="border-t border-gray-200 p-4 bg-white max-h-80 overflow-y-auto">
                    <TaskHistory history={currentTask.history} />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}