import React, { useState, useEffect } from 'react';
import { Trash2, RotateCcw, FolderOpen, FileText, Calendar, User, AlertTriangle } from 'lucide-react';
import { archiveService } from '../services/archiveService';
import { ArchivedItem, ArchiveStats } from '../types';
import { useSupabaseStore } from '../store/useSupabaseStore';

export default function ArchiveManager() {
  const { syncData } = useSupabaseStore();
  const [archivedItems, setArchivedItems] = useState<ArchivedItem[]>([]);
  const [archiveStats, setArchiveStats] = useState<ArchiveStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [filter, setFilter] = useState<'all' | 'folder' | 'project' | 'task'>('all');

  useEffect(() => {
    loadArchiveData();
  }, []);

  const loadArchiveData = async () => {
    try {
      setLoading(true);
      const [items, stats] = await Promise.all([
        archiveService.getArchivedItems(),
        archiveService.getArchiveStats()
      ]);
      setArchivedItems(items);
      setArchiveStats(stats);
    } catch (error) {
      console.error('Failed to load archive data:', error);
      alert('Failed to load archive data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRestore = async (item: ArchivedItem) => {
    if (!window.confirm(`Are you sure you want to restore "${item.name}"? This will move it back to its original location.`)) {
      return;
    }

    try {
      switch (item.type) {
        case 'folder':
          await archiveService.restoreFolder(item.id);
          break;
        case 'project':
          await archiveService.restoreProject(item.id);
          break;
        case 'task':
          await archiveService.restoreTask(item.id);
          break;
      }

      // Refresh archive data
      await loadArchiveData();

      // Sync main application data so restored items appear immediately
      await syncData();

      alert(`${item.type} "${item.name}" has been restored successfully.`);
    } catch (error) {
      console.error('Failed to restore item:', error);
      alert('Failed to restore item. Please try again.');
    }
  };

  const handleEmptyArchive = async () => {
    if (!window.confirm('Are you sure you want to permanently delete ALL archived items? This action cannot be undone.')) {
      return;
    }

    if (!window.confirm('This will permanently delete all archived folders, projects, and tasks. Are you absolutely sure?')) {
      return;
    }

    try {
      await archiveService.emptyArchive();
      await loadArchiveData();

      // Sync main application data in case any items were affected
      await syncData();

      alert('Archive has been emptied successfully.');
    } catch (error) {
      console.error('Failed to empty archive:', error);
      alert('Failed to empty archive. Please try again.');
    }
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'folder':
        return <FolderOpen className="w-4 h-4 text-blue-400" />;
      case 'project':
        return <div className="w-3 h-3 rounded-full bg-green-400" />;
      case 'task':
        return <FileText className="w-4 h-4 text-gray-400" />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDaysUntilExpiry = (archivedAt: string) => {
    const archived = new Date(archivedAt);
    const expiry = new Date(archived.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days
    const now = new Date();
    const daysLeft = Math.ceil((expiry.getTime() - now.getTime()) / (24 * 60 * 60 * 1000));
    return Math.max(0, daysLeft);
  };

  const filteredItems = archivedItems.filter(item => 
    filter === 'all' || item.type === filter
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading archive...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">Archive Management</h1>
            <p className="text-gray-400">Manage archived folders, projects, and tasks</p>
          </div>
          <button
            onClick={handleEmptyArchive}
            disabled={!archiveStats || archiveStats.totalItems === 0}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <Trash2 className="w-4 h-4" />
            Empty Archive
          </button>
        </div>

        {/* Stats */}
        {archiveStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Trash2 className="w-5 h-5 text-gray-400" />
                <span className="text-sm text-gray-400">Total Items</span>
              </div>
              <div className="text-2xl font-bold">{archiveStats.totalItems}</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <FolderOpen className="w-5 h-5 text-blue-400" />
                <span className="text-sm text-gray-400">Folders</span>
              </div>
              <div className="text-2xl font-bold">{archiveStats.folders}</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-4 h-4 rounded-full bg-green-400" />
                <span className="text-sm text-gray-400">Projects</span>
              </div>
              <div className="text-2xl font-bold">{archiveStats.projects}</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="w-5 h-5 text-gray-400" />
                <span className="text-sm text-gray-400">Tasks</span>
              </div>
              <div className="text-2xl font-bold">{archiveStats.tasks}</div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="flex gap-2 mb-6">
          {(['all', 'folder', 'project', 'task'] as const).map((filterType) => (
            <button
              key={filterType}
              onClick={() => setFilter(filterType)}
              className={`px-3 py-1 rounded-lg text-sm ${
                filter === filterType
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              {filterType === 'all' ? 'All Items' : `${filterType.charAt(0).toUpperCase()}${filterType.slice(1)}s`}
            </button>
          ))}
        </div>

        {/* Items List */}
        {filteredItems.length === 0 ? (
          <div className="text-center py-12">
            <Trash2 className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-400 mb-2">No archived items</h3>
            <p className="text-gray-500">Deleted items will appear here and be automatically removed after 1 week.</p>
          </div>
        ) : (
          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Item
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Archived By
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Archived Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Expires In
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {filteredItems.map((item) => {
                    const daysLeft = getDaysUntilExpiry(item.archivedAt);
                    return (
                      <tr key={item.id} className="hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-3">
                            {getIcon(item.type)}
                            <div>
                              <div className="text-sm font-medium text-white">{item.name}</div>
                              <div className="text-sm text-gray-400">{item.originalLocation}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 text-xs font-medium bg-gray-600 text-gray-300 rounded-full">
                            {item.type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          <div className="flex items-center gap-2">
                            <User className="w-4 h-4" />
                            {item.archivedBy}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            {formatDate(item.archivedAt)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <div className={`flex items-center gap-2 ${daysLeft <= 1 ? 'text-red-400' : 'text-gray-300'}`}>
                            {daysLeft <= 1 && <AlertTriangle className="w-4 h-4" />}
                            {daysLeft === 0 ? 'Expires today' : `${daysLeft} day${daysLeft !== 1 ? 's' : ''}`}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <button
                            onClick={() => handleRestore(item)}
                            className="flex items-center gap-2 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                          >
                            <RotateCcw className="w-4 h-4" />
                            Restore
                          </button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
