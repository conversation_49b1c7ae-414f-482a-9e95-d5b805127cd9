/**
 * Task Navigation Service
 * Centralized service for handling task opening and navigation
 */

import { Task, Subtask } from '../types';
import { navigateToTask } from '../utils/navigationUtils';

export interface TaskNavigationOptions {
  view?: 'edit' | 'view';
  openInModal?: boolean;
  markNotificationAsRead?: boolean;
  notificationId?: string;
}

export interface TaskOpenResult {
  success: boolean;
  task?: Task;
  subtask?: Subtask;
  error?: string;
}

class TaskNavigationService {
  private static instance: TaskNavigationService;
  
  private constructor() {}
  
  public static getInstance(): TaskNavigationService {
    if (!TaskNavigationService.instance) {
      TaskNavigationService.instance = new TaskNavigationService();
    }
    return TaskNavigationService.instance;
  }

  /**
   * Open a task by ID with navigation
   */
  async openTask(
    taskId: string, 
    options: TaskNavigationOptions = {}
  ): Promise<TaskOpenResult> {
    try {
      // Navigate to the task using URL parameters
      navigateToTask(taskId, undefined, options.view);
      
      return {
        success: true
      };
    } catch (error) {
      console.error('Failed to open task:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Open a subtask by parent task ID and subtask ID
   */
  async openSubtask(
    taskId: string,
    subtaskId: string,
    options: TaskNavigationOptions = {}
  ): Promise<TaskOpenResult> {
    try {
      // Navigate to the parent task with subtask parameter
      navigateToTask(taskId, subtaskId, options.view);
      
      return {
        success: true
      };
    } catch (error) {
      console.error('Failed to open subtask:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Open task from notification (includes marking as read)
   */
  async openTaskFromNotification(
    taskId: string,
    notificationId?: string,
    options: TaskNavigationOptions = {}
  ): Promise<TaskOpenResult> {
    try {
      // Mark notification as read if specified
      if (notificationId && options.markNotificationAsRead) {
        // Import the store dynamically to avoid circular dependencies
        const { useSupabaseStore } = await import('../store/useSupabaseStore');
        const store = useSupabaseStore.getState();
        
        try {
          await store.markNotificationAsRead(notificationId);
        } catch (error) {
          console.warn('Failed to mark notification as read:', error);
          // Don't fail the whole operation if notification marking fails
        }
      }

      // Navigate to the task
      return await this.openTask(taskId, options);
    } catch (error) {
      console.error('Failed to open task from notification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Validate if a task exists and user has access
   */
  async validateTaskAccess(taskId: string): Promise<boolean> {
    try {
      // Import the store dynamically to avoid circular dependencies
      const { useSupabaseStore } = await import('../store/useSupabaseStore');
      const store = useSupabaseStore.getState();
      
      // Check if task exists in current store
      const task = store.tasks.find(t => t.id === taskId);
      
      if (!task) {
        // Task not found in current store, might need to sync
        console.warn(`Task ${taskId} not found in current store`);
        return false;
      }
      
      // Task exists and user has access (if it's in the store, user can access it due to RLS)
      return true;
    } catch (error) {
      console.error('Failed to validate task access:', error);
      return false;
    }
  }

  /**
   * Get task details by ID
   */
  async getTaskById(taskId: string): Promise<Task | null> {
    try {
      // Import the store dynamically to avoid circular dependencies
      const { useSupabaseStore } = await import('../store/useSupabaseStore');
      const store = useSupabaseStore.getState();
      
      return store.tasks.find(t => t.id === taskId) || null;
    } catch (error) {
      console.error('Failed to get task by ID:', error);
      return null;
    }
  }

  /**
   * Get subtask details by parent task ID and subtask ID
   */
  async getSubtaskById(taskId: string, subtaskId: string): Promise<Subtask | null> {
    try {
      const task = await this.getTaskById(taskId);
      if (!task || !task.subtasks) {
        return null;
      }
      
      return task.subtasks.find(s => s.id === subtaskId) || null;
    } catch (error) {
      console.error('Failed to get subtask by ID:', error);
      return null;
    }
  }

  /**
   * Handle navigation from external sources (like email links, etc.)
   */
  async handleExternalNavigation(
    taskId: string,
    subtaskId?: string,
    source?: string
  ): Promise<TaskOpenResult> {
    try {
      console.log(`Handling external navigation to task ${taskId} from ${source || 'unknown source'}`);
      
      // Validate access first
      const hasAccess = await this.validateTaskAccess(taskId);
      if (!hasAccess) {
        return {
          success: false,
          error: 'Task not found or access denied'
        };
      }

      // Open the task or subtask
      if (subtaskId) {
        return await this.openSubtask(taskId, subtaskId, { view: 'edit' });
      } else {
        return await this.openTask(taskId, { view: 'edit' });
      }
    } catch (error) {
      console.error('Failed to handle external navigation:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create a shareable link for a task
   */
  createShareableLink(taskId: string, subtaskId?: string): string {
    const baseUrl = window.location.origin + window.location.pathname;
    const params = new URLSearchParams();
    
    params.set('taskId', taskId);
    if (subtaskId) {
      params.set('subtaskId', subtaskId);
    }
    
    return `${baseUrl}#tasks?${params.toString()}`;
  }

  /**
   * Get user-friendly share message
   */
  async getShareMessage(taskId: string, subtaskId?: string): Promise<string> {
    try {
      const task = await this.getTaskById(taskId);
      if (!task) {
        return `Check out this task: ${this.createShareableLink(taskId, subtaskId)}`;
      }

      let title = task.title;
      if (subtaskId) {
        const subtask = await this.getSubtaskById(taskId, subtaskId);
        if (subtask) {
          title = `${task.title} - ${subtask.title}`;
        }
      }

      return `Check out this task: "${title}"\n${this.createShareableLink(taskId, subtaskId)}`;
    } catch (error) {
      console.error('Failed to get share message:', error);
      return `Check out this task: ${this.createShareableLink(taskId, subtaskId)}`;
    }
  }
}

// Export singleton instance
export const taskNavigationService = TaskNavigationService.getInstance();
export default taskNavigationService;
