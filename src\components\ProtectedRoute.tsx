import React from 'react';
import { Loader2 } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import AuthPage from './auth/AuthPage';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
}

export default function ProtectedRoute({ children, requireAdmin = false }: ProtectedRouteProps) {
  const { user, profile, loading, initialized, isAdmin } = useAuth();

  // Show loading spinner while initializing
  if (!initialized || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show auth page if not authenticated
  if (!user || !profile) {
    // Store the current URL (including hash and parameters) for redirect after login
    const currentPath = window.location.pathname + window.location.search + window.location.hash;

    // Only store if it contains meaningful content (like task parameters)
    if (currentPath !== '/' && currentPath !== '' &&
        (currentPath.includes('taskId=') || currentPath.includes('#tasks') || currentPath.includes('#inbox'))) {
      // Clean the path of any timestamp parameters before storing
      const cleanPath = currentPath.replace(/[?&]t=\d+/g, '');
      sessionStorage.setItem('redirectAfterLogin', cleanPath);
    }

    return <AuthPage />;
  }

  // Check admin requirement
  if (requireAdmin && !isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    );
  }

  // Render protected content
  return <>{children}</>;
}
