/**
 * RecurrencePreview - Component to preview next execution dates
 * Shows upcoming execution dates and pattern description to help users validate their configuration
 */

import React from 'react';
import { Calendar, Clock, AlertTriangle, CheckCircle, Info } from 'lucide-react';
import { RecurrencePreview as RecurrencePreviewType } from '../types/recurrence';
import { formatDateForTimezone } from '../utils/recurrenceUtils';

interface RecurrencePreviewProps {
  preview: RecurrencePreviewType;
  timezone?: string;
  className?: string;
}

export default function RecurrencePreview({ 
  preview, 
  timezone = 'UTC', 
  className = '' 
}: RecurrencePreviewProps) {
  
  if (!preview.isValid) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <h4 className="font-medium text-red-900 mb-2">Invalid <PERSON></h4>
            {preview.validationErrors && preview.validationErrors.length > 0 && (
              <ul className="text-sm text-red-700 space-y-1">
                {preview.validationErrors.map((error, index) => (
                  <li key={index} className="flex items-start gap-1">
                    <span className="text-red-500">•</span>
                    <span>{error}</span>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="space-y-4">
        {/* Pattern Description */}
        <div className="flex items-start gap-3">
          <CheckCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <h4 className="font-medium text-blue-900 mb-1">Recurrence Pattern</h4>
            <p className="text-sm text-blue-800">{preview.description}</p>
          </div>
        </div>

        {/* Next Execution Dates */}
        {preview.nextExecutions && preview.nextExecutions.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-blue-600" />
              <h5 className="font-medium text-blue-900">Next Executions</h5>
            </div>
            
            <div className="space-y-2">
              {preview.nextExecutions.slice(0, 5).map((date, index) => (
                <div 
                  key={index} 
                  className="flex items-center gap-3 text-sm bg-white rounded-lg p-3 border border-blue-100"
                >
                  <div className="flex items-center gap-2 text-blue-700">
                    <Clock className="w-4 h-4" />
                    <span className="font-medium">#{index + 1}</span>
                  </div>
                  
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      {formatDateForTimezone(date, timezone)}
                    </div>
                    <div className="text-gray-600">
                      {getRelativeTimeDescription(date)}
                    </div>
                  </div>
                  
                  {index === 0 && (
                    <div className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
                      Next
                    </div>
                  )}
                </div>
              ))}
            </div>

            {preview.nextExecutions.length > 5 && (
              <div className="text-center">
                <div className="inline-flex items-center gap-1 text-sm text-blue-700 bg-blue-100 px-3 py-1 rounded-full">
                  <Info className="w-4 h-4" />
                  <span>And {preview.nextExecutions.length - 5} more executions...</span>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Timezone Info */}
        {timezone !== 'UTC' && (
          <div className="flex items-center gap-2 text-xs text-blue-700 bg-blue-100 px-3 py-2 rounded-lg">
            <Clock className="w-3 h-3" />
            <span>Times shown in {timezone} timezone</span>
          </div>
        )}

        {/* Empty State */}
        {(!preview.nextExecutions || preview.nextExecutions.length === 0) && (
          <div className="text-center py-4">
            <Calendar className="w-8 h-8 text-blue-400 mx-auto mb-2" />
            <p className="text-sm text-blue-700">No upcoming executions to preview</p>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Get a human-readable relative time description
 */
function getRelativeTimeDescription(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = date.getTime() - now.getTime();
  
  if (diffMs < 0) {
    return 'In the past';
  }
  
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffWeeks = Math.floor(diffDays / 7);
  const diffMonths = Math.floor(diffDays / 30);
  const diffYears = Math.floor(diffDays / 365);

  if (diffMinutes < 60) {
    return `In ${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''}`;
  } else if (diffHours < 24) {
    return `In ${diffHours} hour${diffHours !== 1 ? 's' : ''}`;
  } else if (diffDays < 7) {
    return `In ${diffDays} day${diffDays !== 1 ? 's' : ''}`;
  } else if (diffWeeks < 4) {
    return `In ${diffWeeks} week${diffWeeks !== 1 ? 's' : ''}`;
  } else if (diffMonths < 12) {
    return `In ${diffMonths} month${diffMonths !== 1 ? 's' : ''}`;
  } else {
    return `In ${diffYears} year${diffYears !== 1 ? 's' : ''}`;
  }
}

/**
 * Compact version of RecurrencePreview for smaller spaces
 */
export function CompactRecurrencePreview({ 
  preview, 
  timezone = 'UTC', 
  className = '' 
}: RecurrencePreviewProps) {
  if (!preview.isValid) {
    return (
      <div className={`flex items-center gap-2 text-red-600 ${className}`}>
        <AlertTriangle className="w-4 h-4" />
        <span className="text-sm">Invalid pattern</span>
      </div>
    );
  }

  const nextExecution = preview.nextExecutions?.[0];

  return (
    <div className={`flex items-center gap-2 text-blue-600 ${className}`}>
      <CheckCircle className="w-4 h-4" />
      <div className="text-sm">
        <span className="font-medium">{preview.description}</span>
        {nextExecution && (
          <span className="text-gray-600 ml-2">
            • Next: {formatDateForTimezone(nextExecution, timezone)}
          </span>
        )}
      </div>
    </div>
  );
}

/**
 * Preview card for displaying in lists or grids
 */
export function RecurrencePreviewCard({ 
  preview, 
  timezone = 'UTC', 
  className = '',
  title = 'Recurrence Preview'
}: RecurrencePreviewProps & { title?: string }) {
  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center gap-2 mb-3">
        <Calendar className="w-5 h-5 text-gray-600" />
        <h3 className="font-medium text-gray-900">{title}</h3>
      </div>
      
      <RecurrencePreview preview={preview} timezone={timezone} />
    </div>
  );
}

/**
 * Inline preview for form validation
 */
export function InlineRecurrencePreview({ 
  preview, 
  timezone = 'UTC', 
  className = '' 
}: RecurrencePreviewProps) {
  if (!preview.isValid) {
    return (
      <div className={`text-sm text-red-600 ${className}`}>
        {preview.validationErrors?.[0] || 'Invalid pattern'}
      </div>
    );
  }

  const nextExecution = preview.nextExecutions?.[0];

  return (
    <div className={`text-sm text-gray-600 ${className}`}>
      <span>{preview.description}</span>
      {nextExecution && (
        <span className="block text-blue-600 mt-1">
          Next: {formatDateForTimezone(nextExecution, timezone)}
        </span>
      )}
    </div>
  );
}
