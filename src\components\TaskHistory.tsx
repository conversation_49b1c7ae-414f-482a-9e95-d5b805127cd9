
import { format } from 'date-fns';
import { TaskHistoryEntry } from '../types';
import { Clock, ArrowRight } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { formatValue } from './formatValue';
import Avatar from './Avatar';

interface TaskHistoryProps {
  history: TaskHistoryEntry[];
}



export default function TaskHistory({ history }: TaskHistoryProps) {
  const { columns, users, folders, projects } = useSupabaseStore();

  // Handle undefined history
  const safeHistory = history || [];

  // Helper functions for user information
  const getUserName = (userId: string) => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : 'Unknown User';
  };

  const getUser = (userId: string) => {
    return users.find(u => u.id === userId);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2 sticky top-0 bg-white py-2 z-10">
        <Clock className="w-5 h-5" />
        History
      </h3>

      <div className="space-y-3">
        {safeHistory.length === 0 ? (
          <p className="text-sm text-gray-500">No history yet</p>
        ) : (
          safeHistory
            .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
            .map((entry) => {
              const user = getUser(entry.userId);
              return (
                <div key={entry.id} className="flex items-start gap-3 text-sm">
                  <div className="w-32 flex-shrink-0 text-gray-500">
                    {format(new Date(entry.timestamp), 'MMM d, HH:mm')}
                  </div>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <Avatar
                      src={user?.avatar}
                      name={user?.name || entry.userId}
                      size="sm"
                    />
                    <span className="text-gray-600 text-xs font-medium">
                      {getUserName(entry.userId)}
                    </span>
                  </div>
                  <div className="flex-1">
                    <span className="font-medium capitalize">{entry.field}</span> changed from{' '}
                    <span className="text-gray-600">{formatValue(entry.field, entry.oldValue, columns, users, folders, projects)}</span>
                    <ArrowRight className="w-4 h-4 inline mx-1" />
                    <span className="text-gray-900">{formatValue(entry.field, entry.newValue, columns, users, folders, projects)}</span>
                  </div>
                </div>
              );
            })
        )}
      </div>
    </div>
  );
}
