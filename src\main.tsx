// import { StrictMode } from 'react'; // Temporarily disabled to prevent double initialization
import { createRoot } from 'react-dom/client';
import App from './App';
import './index.css';

// Clear browser cache on load in development (but preserve auth sessions)
if (import.meta.env.DEV) {
  // Only clear localStorage, preserve sessionStorage for auth redirects
  localStorage.clear();

  // Don't clear sessionStorage as it may contain auth redirect info
  // sessionStorage.clear();

  // Unregister any service workers
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
      for(let registration of registrations) {
        registration.unregister();
      }
    });
  }

  // Clear all caches
  if ('caches' in window) {
    caches.keys().then(function(names) {
      for (let name of names) {
        caches.delete(name);
      }
    });
  }
}

const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Root element not found');

createRoot(rootElement).render(
  // Temporarily disable StrictMode to prevent double initialization issues
  // <StrictMode>
    <App />
  // </StrictMode>
);