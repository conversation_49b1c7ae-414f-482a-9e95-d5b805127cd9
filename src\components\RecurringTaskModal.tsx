/**
 * RecurringTaskModal - Main modal component for configuring recurring tasks
 * Provides a user-friendly interface for setting up flexible recurrence patterns
 */

import React, { useState, useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { X, Calendar, Clock, AlertCircle, CheckCircle } from 'lucide-react';
import { 
  RecurrenceFormData, 
  RecurrenceFrequency, 
  CreateRecurrenceInput,
  TaskRecurrence 
} from '../types/recurrence';
import { recurrenceService } from '../services/recurrenceService';
import { generateRecurrencePreview } from '../utils/recurrenceUtils';
import RecurrencePatternSelector from './RecurrencePatternSelector';
import RecurrencePreview from './RecurrencePreview';

interface RecurringTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  taskId: string;
  taskTitle: string;
  existingRecurrence?: TaskRecurrence;
  onSuccess?: (recurrence: TaskRecurrence) => void;
}

export default function RecurringTaskModal({
  isOpen,
  onClose,
  taskId,
  taskTitle,
  existingRecurrence,
  onSuccess
}: RecurringTaskModalProps) {
  const [formData, setFormData] = useState<RecurrenceFormData>({
    name: '',
    description: '',
    frequency: 'daily',
    patternConfig: { type: 'daily', interval: 1 },
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    maxExecutions: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Initialize form data when modal opens or existing recurrence changes
  useEffect(() => {
    if (isOpen) {
      if (existingRecurrence) {
        setFormData({
          name: existingRecurrence.name,
          description: existingRecurrence.description || '',
          frequency: existingRecurrence.frequency,
          patternConfig: existingRecurrence.patternConfig,
          startDate: existingRecurrence.startDate,
          endDate: existingRecurrence.endDate || '',
          timezone: existingRecurrence.timezone,
          maxExecutions: existingRecurrence.maxExecutions?.toString() || ''
        });
      } else {
        // Reset form for new recurrence
        setFormData({
          name: `Recurring: ${taskTitle}`,
          description: '',
          frequency: 'daily',
          patternConfig: { type: 'daily', interval: 1 },
          startDate: new Date().toISOString().split('T')[0],
          endDate: '',
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          maxExecutions: ''
        });
      }
      setError(null);
      setValidationErrors({});
    }
  }, [isOpen, existingRecurrence, taskTitle]);

  const handleFrequencyChange = (frequency: RecurrenceFrequency) => {
    setFormData(prev => ({
      ...prev,
      frequency,
      patternConfig: getDefaultPatternForFrequency(frequency)
    }));
  };

  const handlePatternConfigChange = (patternConfig: any) => {
    setFormData(prev => ({
      ...prev,
      patternConfig
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setValidationErrors({});

    try {
      // Validate form
      const errors = validateForm(formData);
      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        setIsSubmitting(false);
        return;
      }

      // Prepare input data
      const input: CreateRecurrenceInput = {
        taskId,
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        frequency: formData.frequency,
        patternConfig: formData.patternConfig,
        startDate: formData.startDate,
        endDate: formData.endDate || undefined,
        timezone: formData.timezone,
        maxExecutions: formData.maxExecutions ? parseInt(formData.maxExecutions) : undefined
      };

      let result;
      if (existingRecurrence) {
        // Update existing recurrence
        result = await recurrenceService.updateRecurrence(existingRecurrence.id, input);
      } else {
        // Create new recurrence
        result = await recurrenceService.createRecurrence(input);
      }

      if (result.success && result.data) {
        onSuccess?.(result.data);
        onClose();
      } else {
        setError(result.error || 'Failed to save recurrence');
        if (result.validationErrors) {
          setValidationErrors(result.validationErrors);
        }
      }
    } catch (error) {
      console.error('Error saving recurrence:', error);
      setError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const preview = generateRecurrencePreview(
    formData.patternConfig,
    formData.startDate,
    formData.timezone
  );

  if (!isOpen) return null;

  return createPortal(
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Calendar className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {existingRecurrence ? 'Edit Recurring Task' : 'Set Recurring Task'}
              </h2>
              <p className="text-sm text-gray-600">{taskTitle}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Display */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Recurrence Name *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  validationErrors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter a name for this recurring task"
              />
              {validationErrors.name && (
                <p className="text-sm text-red-600 mt-1">{validationErrors.name}</p>
              )}
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Optional description for this recurrence"
              />
            </div>
          </div>

          {/* Recurrence Pattern */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Recurrence Pattern</h3>
            
            <RecurrencePatternSelector
              frequency={formData.frequency}
              patternConfig={formData.patternConfig}
              onFrequencyChange={handleFrequencyChange}
              onPatternConfigChange={handlePatternConfigChange}
              validationErrors={validationErrors}
            />
          </div>

          {/* Schedule Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Schedule Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date *
                </label>
                <input
                  type="date"
                  id="startDate"
                  value={formData.startDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    validationErrors.startDate ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {validationErrors.startDate && (
                  <p className="text-sm text-red-600 mt-1">{validationErrors.startDate}</p>
                )}
              </div>

              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                  End Date (Optional)
                </label>
                <input
                  type="date"
                  id="endDate"
                  value={formData.endDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 mb-1">
                  Timezone
                </label>
                <select
                  id="timezone"
                  value={formData.timezone}
                  onChange={(e) => setFormData(prev => ({ ...prev, timezone: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="UTC">UTC</option>
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                  <option value="Europe/London">London</option>
                  <option value="Europe/Paris">Paris</option>
                  <option value="Asia/Tokyo">Tokyo</option>
                </select>
              </div>

              <div>
                <label htmlFor="maxExecutions" className="block text-sm font-medium text-gray-700 mb-1">
                  Max Executions (Optional)
                </label>
                <input
                  type="number"
                  id="maxExecutions"
                  value={formData.maxExecutions}
                  onChange={(e) => setFormData(prev => ({ ...prev, maxExecutions: e.target.value }))}
                  min="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Unlimited"
                />
              </div>
            </div>
          </div>

          {/* Preview */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Preview</h3>
            <RecurrencePreview preview={preview} />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <Clock className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4" />
                  {existingRecurrence ? 'Update Recurrence' : 'Create Recurrence'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>,
    document.body
  );
}

// Helper functions
function getDefaultPatternForFrequency(frequency: RecurrenceFrequency) {
  switch (frequency) {
    case 'daily':
      return { type: 'daily', interval: 1 };
    case 'weekly':
      return { type: 'weekly', interval: 1, daysOfWeek: [new Date().getDay()] };
    case 'monthly':
      return { type: 'monthly', interval: 1, dayOfMonth: new Date().getDate() };
    case 'yearly':
      return { type: 'yearly', interval: 1, month: new Date().getMonth() + 1, dayOfMonth: new Date().getDate() };
    case 'custom':
      return { type: 'custom' };
    default:
      return { type: 'daily', interval: 1 };
  }
}

function validateForm(formData: RecurrenceFormData): Record<string, string> {
  const errors: Record<string, string> = {};

  if (!formData.name.trim()) {
    errors.name = 'Name is required';
  }

  if (!formData.startDate) {
    errors.startDate = 'Start date is required';
  } else {
    const startDate = new Date(formData.startDate);
    if (isNaN(startDate.getTime())) {
      errors.startDate = 'Invalid start date';
    }
  }

  if (formData.endDate) {
    const endDate = new Date(formData.endDate);
    const startDate = new Date(formData.startDate);
    if (isNaN(endDate.getTime())) {
      errors.endDate = 'Invalid end date';
    } else if (endDate <= startDate) {
      errors.endDate = 'End date must be after start date';
    }
  }

  if (formData.maxExecutions && parseInt(formData.maxExecutions) < 1) {
    errors.maxExecutions = 'Max executions must be at least 1';
  }

  return errors;
}
