import React, { useState, useEffect } from 'react';
import { Inbox as InboxIcon, CheckCircle2, Trash2, Refresh<PERSON>w } from 'lucide-react';
import { Notification } from '../types';
import { notificationService } from '../services/supabaseService';
import { getCurrentUser } from '../lib/supabase';
import NotificationItem from './NotificationItem';

export default function Inbox() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  useEffect(() => {
    const initializeUser = async () => {
      try {
        const user = await getCurrentUser();
        setCurrentUser(user);
        if (user) {
          await loadNotifications(user.id);
        }
      } catch (error) {
        console.error('Failed to get current user:', error);
      }
    };

    initializeUser();
  }, []);

  const loadNotifications = async (userId: string) => {
    try {
      setLoading(true);
      const data = await notificationService.getNotifications(userId);
      
      // Transform database format to component format
      const transformedNotifications: Notification[] = data.map(item => ({
        id: item.id,
        recipientId: item.recipient_id,
        senderId: item.sender_id,
        type: item.type,
        title: item.title,
        content: item.content,
        taskId: item.task_id || undefined,
        commentId: item.comment_id || undefined,
        isRead: item.is_read,
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }));
      
      setNotifications(transformedNotifications);
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, isRead: true }
            : notification
        )
      );
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleMarkAsUnread = async (notificationId: string) => {
    try {
      await notificationService.markAsUnread(notificationId);
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, isRead: false }
            : notification
        )
      );
    } catch (error) {
      console.error('Failed to mark notification as unread:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!currentUser) return;
    
    try {
      await notificationService.markAllAsRead(currentUser.id);
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, isRead: true }))
      );
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const handleNavigateToTask = async (taskId: string) => {
    try {
      const { taskNavigationService } = await import('../services/taskNavigationService');
      const result = await taskNavigationService.openTask(taskId, { view: 'edit' });

      if (!result.success) {
        console.error('Failed to navigate to task:', result.error);
        alert('Failed to open task. It may have been deleted or you may not have access.');
      }
    } catch (error) {
      console.error('Failed to navigate to task:', error);
      alert('Failed to open task. Please try again.');
    }
  };

  const handleRefresh = () => {
    if (currentUser) {
      loadNotifications(currentUser.id);
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') {
      return !notification.isRead;
    }
    return true;
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500">Loading notifications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <InboxIcon className="w-5 h-5 text-gray-700" />
            <h1 className="text-lg font-semibold text-gray-900">Inbox</h1>
            {unreadCount > 0 && (
              <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                {unreadCount}
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleRefresh}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              title="Refresh"
            >
              <RefreshCw className="w-4 h-4 text-gray-600" />
            </button>
            
            {unreadCount > 0 && (
              <button
                onClick={handleMarkAllAsRead}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                title="Mark all as read"
              >
                <CheckCircle2 className="w-4 h-4 text-green-600" />
              </button>
            )}
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="flex gap-1 mt-3">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              filter === 'all'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            All ({notifications.length})
          </button>
          <button
            onClick={() => setFilter('unread')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              filter === 'unread'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            Unread ({unreadCount})
          </button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="flex-1 overflow-y-auto">
        {filteredNotifications.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <InboxIcon className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">
                {filter === 'unread' ? 'No unread notifications' : 'No notifications yet'}
              </p>
              <p className="text-sm text-gray-400 mt-1">
                You'll see mentions and updates here
              </p>
            </div>
          </div>
        ) : (
          <div>
            {filteredNotifications.map(notification => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onMarkAsRead={handleMarkAsRead}
                onMarkAsUnread={handleMarkAsUnread}
                onNavigateToTask={handleNavigateToTask}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
