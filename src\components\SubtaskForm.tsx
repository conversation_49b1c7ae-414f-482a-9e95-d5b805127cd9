import React, { useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Subtask, Task, TaskEffort } from '../types';
import { ChevronDown, ChevronRight, MoreVertical, Copy, Share2 } from 'lucide-react';
import { useSupabaseStore } from '../store/useSupabaseStore';
import TaskHistory from './TaskHistory';
import TaskComments from './TaskComments';
import CompactTimelineStats from './CompactTimelineStats';
import TaskEffortEstimator from './TaskEffortEstimator';
import CustomFieldsSection from './CustomFieldsSection';
import { validateAllCustomFieldValues } from '../types/customFields';
import ClickableStatusBadge from './ClickableStatusBadge';
import ClickablePriorityBadge from './ClickablePriorityBadge';
import ClickableProjectBadge from './ClickableProjectBadge';
import CompactAssignmentField from './CompactAssignmentField';
import RichTextEditor from './RichTextEditor';

interface SubtaskFormProps {
  onSubmit: (subtask: Omit<Subtask, 'id'>) => void;
  onClose: () => void;
  initialData?: Subtask | null;
  taskId: string;
  parentTaskStatus: Task['status'];
}

export default function SubtaskForm({ onSubmit, onClose, initialData, taskId, parentTaskStatus }: SubtaskFormProps) {
  const { users, userGroups, customFields, columns, projects, cloneTask } = useSupabaseStore();

  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    description: initialData?.description || '',
    completed: initialData?.completed || false,
    assignedUserId: initialData?.assignedUserId || '',
    assignedUsers: initialData?.assignedUsers || [] as string[],
    priority: initialData?.priority || 'medium' as Task['priority'],
    startDate: initialData?.startDate || '',
    dueDate: initialData?.dueDate || '',
    assignedGroups: initialData?.assignedGroups || [] as string[],
    ownerId: initialData?.ownerId || '',
    status: parentTaskStatus, // Always use parent task's status
    comments: initialData?.comments || [],
    history: initialData?.history || [],
    durations: initialData?.durations || [],
    effort: initialData?.effort || undefined,
    customFieldValues: initialData?.customFieldValues || {},
  });

  const [isAdvancedExpanded, setIsAdvancedExpanded] = useState(false);
  const [isEffortExpanded, setIsEffortExpanded] = useState(false);
  const [isHistoryExpanded, setIsHistoryExpanded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customFieldErrors, setCustomFieldErrors] = useState<Record<string, string>>({});
  const [showActionsMenu, setShowActionsMenu] = useState(false);

  const handleEffortChange = useCallback((effort: TaskEffort) => {
    setFormData(prev => ({ ...prev, effort }));
  }, []);

  const handleCloneTask = async () => {
    try {
      await cloneTask(taskId); // Clone the parent task which includes all subtasks
      setShowActionsMenu(false);
      onClose(); // Close the form after cloning
    } catch (error) {
      console.error('Failed to clone task:', error);
      alert('Failed to clone task. Please try again.');
    }
  };

  const handleShareTask = async () => {
    try {
      const { generateTaskShareUrl, copyToClipboard } = await import('../utils/navigationUtils');

      // For subtasks, we share the parent task with subtask ID
      const subtaskId = initialData?.id;
      const shareLink = generateTaskShareUrl(taskId, subtaskId);

      const success = await copyToClipboard(shareLink.url);

      if (success) {
        alert('Subtask link copied to clipboard!');
      } else {
        // Fallback: show the link in a prompt
        prompt('Copy this link to share the subtask:', shareLink.url);
      }

      setShowActionsMenu(false);
    } catch (error) {
      console.error('Failed to share subtask:', error);
      alert('Failed to generate share link. Please try again.');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return; // Prevent double submissions

    // Validate custom fields
    const activeFields = customFields?.filter((f: any) => f.isActive) || [];
    const validation = validateAllCustomFieldValues(activeFields, formData.customFieldValues);

    if (!validation.isValid) {
      setCustomFieldErrors(validation.errors);
      return;
    }

    // Clear any previous custom field errors
    setCustomFieldErrors({});

    setIsSubmitting(true);
    try {
      onSubmit({
        ...formData,
        comments: formData.comments || [],
        history: formData.history || [],
        durations: formData.durations || []
      });
    } finally {
      // Reset after a short delay to allow the form to close
      setTimeout(() => setIsSubmitting(false), 1000);
    }
  };

  // Handle dropdown toggles
  const handleUserToggle = (userId: string) => {
    const newUsers = formData.assignedUsers.includes(userId)
      ? formData.assignedUsers.filter(id => id !== userId)
      : [...formData.assignedUsers, userId];
    setFormData({ ...formData, assignedUsers: newUsers });
  };

  const handleGroupToggle = (groupId: string) => {
    const newGroups = formData.assignedGroups.includes(groupId)
      ? formData.assignedGroups.filter(id => id !== groupId)
      : [...formData.assignedGroups, groupId];
    setFormData({ ...formData, assignedGroups: newGroups });
  };

  return createPortal(
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center pt-4 overflow-auto z-50"
      onClick={() => {
        setShowActionsMenu(false);
        onClose();
      }}
    >
      <div
        className="bg-white rounded-xl shadow-2xl w-full max-w-[1400px] mb-8 mx-4"
        onClick={(e) => {
          e.stopPropagation();
          setShowActionsMenu(false);
        }}
      >
        {/* Sticky Header */}
        <div className="sticky top-0 bg-white rounded-t-xl border-b border-gray-200 p-6 z-10">
          <div className="flex items-center justify-between gap-4">
            {/* Left side: Title, Status, Priority */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <input
                type="text"
                required
                placeholder={initialData ? "Subtask title..." : "Enter subtask title..."}
                className="text-xl font-semibold border-none outline-none bg-transparent placeholder-gray-400 flex-1 min-w-0"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                title={formData.title} // Show full title on hover
              />
              <ClickableProjectBadge
                projectId={formData.projectId}
                projects={projects}
                onChange={(projectId) => setFormData({ ...formData, projectId })}
              />
              <ClickableStatusBadge
                status={formData.status}
                columns={columns}
                onChange={(status) => setFormData({ ...formData, status })}
              />
              <ClickablePriorityBadge
                priority={formData.priority}
                onChange={(priority) => setFormData({ ...formData, priority })}
              />
            </div>

            {/* Right side: Action buttons */}
            <div className="flex items-center gap-2 flex-shrink-0">
              {/* Three-dot menu for existing subtasks */}
              {initialData && (
                <div className="relative">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowActionsMenu(!showActionsMenu);
                    }}
                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="More actions"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </button>

                  {showActionsMenu && (
                    <div
                      className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-50 min-w-[160px]"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <button
                        type="button"
                        onClick={handleShareTask}
                        className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <Share2 className="w-4 h-4" />
                        Share Subtask
                      </button>
                      <button
                        type="button"
                        onClick={handleCloneTask}
                        className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      >
                        <Copy className="w-4 h-4" />
                        Clone Parent Task
                      </button>
                    </div>
                  )}
                </div>
              )}

              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors font-medium text-sm"
              >
                Cancel
              </button>
              <button
                type="submit"
                form="subtask-form"
                disabled={isSubmitting}
                className={`px-4 py-2 text-white rounded-lg transition-colors font-medium text-sm shadow-sm ${
                  isSubmitting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isSubmitting
                  ? 'Saving...'
                  : (initialData ? 'Update Subtask' : 'Create Subtask')
                }
              </button>
            </div>
          </div>

          {/* Compact Timeline Stats */}
          {initialData && (
            <div className="mt-4">
              <CompactTimelineStats
                durations={initialData.durations || []}
                currentStatus={parentTaskStatus}
              />
            </div>
          )}
        </div>

        {/* Scrollable Content */}
        <div className="p-6 pt-0">

          <div className="space-y-6">
            {/* Main Form Section - Full Width */}
            <form id="subtask-form" onSubmit={handleSubmit} className="space-y-6">
              {/* Main Properties - Compact 4-Column Layout */}
              <div className="bg-gray-50 p-5 rounded-xl border border-gray-200">
                <div className="grid grid-cols-4 gap-4">
                  {/* Dates Section */}
                  <div>
                    <label className="block text-sm font-medium mb-1">Start Date</label>
                    <input
                      type="date"
                      className="w-full border rounded-lg p-2 text-sm"
                      value={formData.startDate}
                      onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Due Date</label>
                    <input
                      type="date"
                      className="w-full border rounded-lg p-2 text-sm"
                      value={formData.dueDate}
                      onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                    />
                  </div>

                  {/* Assignment Section - Light Blue Background */}
                  <div className="bg-blue-50 p-3 rounded-lg border border-blue-200 -m-3 col-span-2">
                    <div className="grid grid-cols-2 gap-4">
                      <CompactAssignmentField
                        label="Primary Assignee"
                        selectedUserId={formData.assignedUserId}
                        users={users}
                        additionalUsers={formData.assignedUsers}
                        onUserChange={(userId) => setFormData({ ...formData, assignedUserId: userId })}
                        onAdditionalUsersChange={(userIds) => setFormData({ ...formData, assignedUsers: userIds })}
                        showAdvanced={true}
                      />

                      <CompactAssignmentField
                        label="Owner"
                        selectedUserId={formData.ownerId}
                        users={users}
                        groups={formData.assignedGroups}
                        availableGroups={userGroups}
                        onUserChange={(userId) => setFormData({ ...formData, ownerId: userId })}
                        onGroupsChange={(groupIds) => setFormData({ ...formData, assignedGroups: groupIds })}
                        showAdvanced={true}
                      />
                    </div>
                  </div>
                </div>
              </div>



              {/* Description */}
              <div>
                <label className="block text-sm font-medium mb-1">📝 Description</label>
                <RichTextEditor
                  value={formData.description}
                  onChange={(value) => setFormData({ ...formData, description: value })}
                  placeholder="Enter subtask description..."
                />
              </div>

              {/* Advanced Settings - Collapsible */}
              <div className="border rounded-lg bg-gray-50">
                <button
                  type="button"
                  onClick={() => setIsAdvancedExpanded(!isAdvancedExpanded)}
                  className="flex items-center gap-2 w-full text-left p-3 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  {isAdvancedExpanded ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                  <span className="font-medium text-gray-700">⚙️ Advanced Settings</span>
                  <span className="text-xs text-gray-500 ml-auto">
                    Effort, Custom Fields
                  </span>
                </button>

                {isAdvancedExpanded && (
                  <div className="border-t bg-white rounded-b-lg">
                    <div className="p-3 space-y-3">
                      {/* Effort Estimation */}
                      <div className="border rounded border-gray-200">
                        <button
                          type="button"
                          onClick={() => setIsEffortExpanded(!isEffortExpanded)}
                          className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-50 rounded transition-colors"
                        >
                          {isEffortExpanded ? (
                            <ChevronDown className="w-3 h-3" />
                          ) : (
                            <ChevronRight className="w-3 h-3" />
                          )}
                          <span className="text-sm font-medium">Effort Estimation</span>
                          {formData.effort && (
                            <span className="ml-auto text-xs text-gray-500">
                              {formData.effort.estimatedHours}h estimated
                            </span>
                          )}
                        </button>

                        {isEffortExpanded && (
                          <div className="border-t p-2">
                            <TaskEffortEstimator
                              taskId={initialData?.id}
                              initialEffort={formData.effort}
                              onEffortChange={handleEffortChange}
                              durations={initialData?.durations || []}
                              currentStatus={formData.status}
                            />
                          </div>
                        )}
                      </div>

                      {/* Custom Fields */}
                      <div className="border rounded border-gray-200">
                        <div className="p-2">
                          <CustomFieldsSection
                            values={formData.customFieldValues}
                            onChange={(values) => setFormData(prev => ({ ...prev, customFieldValues: values }))}
                            errors={customFieldErrors}
                            disabled={isSubmitting}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

            </form>
          </div>
          {/* Comments Section - Full Width */}
          {initialData && (
            <div className="mt-6">
              <TaskComments taskId={taskId} subtaskId={initialData.id} comments={initialData.comments || []} />
            </div>
          )}

          {/* History Section - Full Width Expandable */}
          {initialData && (
            <div className="mt-6">
              <div className="bg-gray-50 rounded-xl border border-gray-200 overflow-hidden">
                <button
                  type="button"
                  onClick={() => setIsHistoryExpanded(!isHistoryExpanded)}
                  className="flex items-center justify-between w-full text-left p-4 hover:bg-gray-100 transition-colors font-medium"
                >
                  <div className="flex items-center gap-2">
                    {isHistoryExpanded ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                    <span>Subtask History</span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {initialData.history?.length || 0} entries
                  </span>
                </button>

                {isHistoryExpanded && (
                  <div className="border-t border-gray-200 p-4 bg-white max-h-80 overflow-y-auto">
                    <TaskHistory history={initialData.history || []} />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>,
    document.body
  );
}