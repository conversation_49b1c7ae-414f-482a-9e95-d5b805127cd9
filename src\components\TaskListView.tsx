import React, { useState, useMemo } from 'react';
import { Task } from '../types';
import { useSupabaseStore } from '../store/useSupabaseStore';
import { Plus, Edit2, Trash2, Calendar, User, Tag, Copy } from 'lucide-react';
import TaskForm from './TaskForm';
import TaskFilterPanel from './TaskFilterPanel';
import { searchText, validateRegexPattern } from '../utils/searchUtils';

export default function TaskListView() {
  const {
    tasks,
    projects,
    folders,
    users,
    userGroups,
    selectedTreeNode,
    taskFilters,
    addTask,
    updateTask,
    deleteTask,
    cloneTask
  } = useSupabaseStore();
  
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);

  // Filter tasks based on selected tree node and additional filters
  const getFilteredTasks = () => {
    // First, filter by tree node selection
    let filteredTasks = tasks;

    if (selectedTreeNode) {
      // Check if selected node is a project
      const selectedProject = projects.find(p => p.id === selectedTreeNode);
      if (selectedProject) {
        filteredTasks = tasks.filter(t => t.projectId === selectedTreeNode);
      } else {
        // Check if selected node is a folder
        const selectedFolder = folders.find(f => f.id === selectedTreeNode);
        if (selectedFolder) {
          const projectsInFolder = projects.filter(p => p.folderId === selectedTreeNode);
          const projectIds = projectsInFolder.map(p => p.id);
          // Include both tasks in projects within the folder AND tasks directly in the folder
          filteredTasks = tasks.filter(t =>
            (t.projectId && projectIds.includes(t.projectId)) || // Tasks in projects within folder
            (t.folderId === selectedTreeNode && !t.projectId)     // Tasks directly in folder
          );
        } else {
          // If it's a task, show just that task
          const selectedTask = tasks.find(t => t.id === selectedTreeNode);
          if (selectedTask) {
            filteredTasks = [selectedTask];
          }
        }
      }
    }

    // Apply additional filters
    return filteredTasks.filter(task => {
      // Filter by assigned users
      if (taskFilters.assignedUsers.length > 0) {
        const hasMatchingUser = taskFilters.assignedUsers.some(userId =>
          task.assignedUsers?.includes(userId) || task.assignedUserId === userId
        );
        if (!hasMatchingUser) return false;
      }

      // Filter by status
      if (taskFilters.status.length > 0) {
        if (!taskFilters.status.includes(task.status)) return false;
      }

      // Filter by priority
      if (taskFilters.priority.length > 0) {
        if (!taskFilters.priority.includes(task.priority)) return false;
      }

      // Filter by assigned groups
      if (taskFilters.assignedGroups.length > 0) {
        const hasMatchingGroup = taskFilters.assignedGroups.some(groupId =>
          task.assignedGroups?.includes(groupId)
        );
        if (!hasMatchingGroup) return false;
      }

      // Filter by tags
      if (taskFilters.tags.length > 0) {
        const hasMatchingTag = taskFilters.tags.some(tag =>
          task.tags.includes(tag)
        );
        if (!hasMatchingTag) return false;
      }

      // Filter by owners
      if (taskFilters.owners.length > 0) {
        if (!task.ownerId || !taskFilters.owners.includes(task.ownerId)) return false;
      }

      // Filter by due date range
      if (taskFilters.dueDateRange.start || taskFilters.dueDateRange.end) {
        if (!task.dueDate) return false;

        const taskDueDate = new Date(task.dueDate);

        if (taskFilters.dueDateRange.start) {
          const startDate = new Date(taskFilters.dueDateRange.start);
          if (taskDueDate < startDate) return false;
        }

        if (taskFilters.dueDateRange.end) {
          const endDate = new Date(taskFilters.dueDateRange.end);
          if (taskDueDate > endDate) return false;
        }
      }

      // Filter by overdue status
      if (taskFilters.hasOverdueTasks) {
        if (!task.dueDate) return false;
        const taskDueDate = new Date(task.dueDate);
        const now = new Date();
        if (taskDueDate >= now || task.status === 'done') return false;
      }

      // Filter by search
      if (taskFilters.search && taskFilters.search.term.trim()) {
        const searchTerm = taskFilters.search.term;
        const isRegex = taskFilters.search.isRegex;

        let searchTarget = '';
        if (taskFilters.search.type === 'task-title') {
          searchTarget = task.title;
        } else if (taskFilters.search.type === 'project-name') {
          const project = projects.find(p => p.id === task.projectId);
          searchTarget = project?.name || '';
        }

        if (isRegex) {
          // Validate regex pattern first
          const validation = validateRegexPattern(searchTerm);
          if (!validation.isValid) {
            return false; // Invalid regex, exclude task
          }

          try {
            const regex = new RegExp(searchTerm, 'i');
            if (!regex.test(searchTarget)) return false;
          } catch (error) {
            return false; // Regex error, exclude task
          }
        } else {
          // Simple text search (case-insensitive)
          const result = searchText(searchTarget, searchTerm, { caseSensitive: false });
          if (!result.matches) return false;
        }
      }

      return true;
    });
  };

  const filteredTasks = getFilteredTasks();

  const getContextInfo = () => {
    if (!selectedTreeNode) return 'All Tasks';
    
    const project = projects.find(p => p.id === selectedTreeNode);
    if (project) return `Project: ${project.name}`;
    
    const folder = folders.find(f => f.id === selectedTreeNode);
    if (folder) return `Folder: ${folder.name}`;
    
    const task = tasks.find(t => t.id === selectedTreeNode);
    if (task) return `Task: ${task.title}`;
    
    return 'All Tasks';
  };

  const handleAddTask = () => {
    setEditingTask(null);
    setShowTaskForm(true);
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setShowTaskForm(true);
  };

  const handleDeleteTask = async (taskId: string) => {
    if (confirm('Are you sure you want to delete this task?')) {
      try {
        await deleteTask(taskId);
      } catch (error) {
        console.error('Failed to delete task:', error);
        alert('Failed to delete task. Please try again.');
      }
    }
  };

  const handleCloneTask = async (taskId: string) => {
    try {
      await cloneTask(taskId);
    } catch (error) {
      console.error('Failed to clone task:', error);
      alert('Failed to clone task. Please try again.');
    }
  };

  const handleTaskSubmit = async (taskData: Omit<Task, 'id'>) => {
    try {
      // Set project context if a project is selected
      const selectedProject = projects.find(p => p.id === selectedTreeNode);
      if (selectedProject) {
        taskData.projectId = selectedProject.id;
      } else {
        // Set folder context if a folder is selected
        const selectedFolder = folders.find(f => f.id === selectedTreeNode);
        if (selectedFolder) {
          taskData.folderId = selectedFolder.id;
          // Ensure projectId is not set when creating a task directly in a folder
          taskData.projectId = undefined;
        }
      }

      if (editingTask) {
        // Extract only the updatable fields for task update
        const { comments, history, durations, subtasks, ...updateData } = taskData;
        await updateTask(editingTask.id, updateData);
      } else {
        await addTask(taskData);
      }

      // Note: syncData() removed to prevent conflicts - local state updates should be sufficient

      setShowTaskForm(false);
      setEditingTask(null);
    } catch (error) {
      console.error('Failed to save task:', error);
      alert('Failed to save task. Please try again.');
    }
  };

  const getStatusBadge = (status: Task['status']) => {
    const colors: { [key: string]: string } = {
      'todo': 'bg-gray-100 text-gray-800',
      'in-progress': 'bg-blue-100 text-blue-800',
      'review': 'bg-yellow-100 text-yellow-800',
      'done': 'bg-green-100 text-green-800'
    };

    // Use predefined color or default for custom statuses
    const colorClass = colors[status] || 'bg-purple-100 text-purple-800';

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
        {status.replace('-', ' ')}
      </span>
    );
  };

  const getPriorityBadge = (priority: Task['priority']) => {
    const colors = {
      'low': 'bg-green-100 text-green-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'high': 'bg-red-100 text-red-800'
    };
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[priority]}`}>
        {priority}
      </span>
    );
  };

  return (
    <div className="flex flex-col">
      {/* Header */}
      <div className="p-6 bg-white border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">{getContextInfo()}</h2>
            <p className="text-sm text-gray-500">{filteredTasks.length} tasks</p>
          </div>
          <button
            onClick={handleAddTask}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Task
          </button>
        </div>
      </div>

      {/* Filter Panel */}
      <TaskFilterPanel />

      {/* Task list */}
      <div className="flex-1">
        {filteredTasks.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-gray-500 mb-4">No tasks found</p>
              <button
                onClick={handleAddTask}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Create your first task
              </button>
            </div>
          </div>
        ) : (
          <div className="p-6">
            <div className="space-y-4">
              {filteredTasks.map((task) => (
                <div
                  key={task.id}
                  className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow group"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-medium text-gray-900">{task.title}</h3>
                        {getStatusBadge(task.status)}
                        {getPriorityBadge(task.priority)}
                      </div>
                      
                      {task.description && (
                        <p className="text-gray-600 text-sm mb-3">{task.description}</p>
                      )}
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        {task.dueDate && (
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {new Date(task.dueDate).toLocaleDateString()}
                          </div>
                        )}
                        
                        {task.assignedUsers && task.assignedUsers.length > 0 && (
                          <div className="flex items-center gap-1">
                            <User className="w-3 h-3" />
                            {task.assignedUsers.length} assigned
                          </div>
                        )}
                        
                        {task.tags.length > 0 && (
                          <div className="flex items-center gap-1">
                            <Tag className="w-3 h-3" />
                            {task.tags.length} tags
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        onClick={() => handleEditTask(task)}
                        className="p-2 hover:bg-gray-100 rounded"
                        title="Edit task"
                      >
                        <Edit2 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleCloneTask(task.id)}
                        className="p-2 hover:bg-blue-50 rounded text-blue-600"
                        title="Clone task"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteTask(task.id)}
                        className="p-2 hover:bg-red-50 rounded text-red-600"
                        title="Delete task"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Task form modal */}
      {showTaskForm && (
        <TaskForm
          onSubmit={handleTaskSubmit}
          onClose={() => {
            setShowTaskForm(false);
            setEditingTask(null);
          }}
          initialData={editingTask || undefined}
        />
      )}
    </div>
  );
}



