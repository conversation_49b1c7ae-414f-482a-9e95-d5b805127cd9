/**
 * RecurrenceAuditService - Admin audit and reporting service for recurring tasks
 * Future-ready service for comprehensive admin dashboard functionality
 * Provides bulk operations, reporting, and system health monitoring
 */

import { supabase } from '../lib/supabase';
import {
  TaskRecurrence,
  TaskRecurrenceExecution,
  RecurrenceStatistics,
  RecurrenceAuditData,
  RecurrenceFilter,
  RecurrenceSearchResult,
  BulkRecurrenceOperation,
  BulkOperationResult,
  RecurrenceServiceResponse
} from '../types/recurrence';

class RecurrenceAuditService {
  /**
   * Get comprehensive system statistics for admin dashboard
   */
  async getSystemStatistics(): Promise<RecurrenceServiceResponse<RecurrenceStatistics>> {
    try {
      const { data, error } = await supabase.rpc('get_recurrence_statistics');
      
      if (error) {
        console.error('Error getting recurrence statistics:', error);
        return { success: false, error: 'Failed to fetch system statistics' };
      }

      return { success: true, data: data[0] };
    } catch (error) {
      console.error('Error in getSystemStatistics:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Search and filter recurrences with pagination (admin view)
   */
  async searchRecurrences(
    filter: RecurrenceFilter = {},
    page: number = 1,
    pageSize: number = 50
  ): Promise<RecurrenceServiceResponse<RecurrenceSearchResult>> {
    try {
      let query = supabase
        .from('task_recurrences')
        .select(`
          *,
          tasks!inner(id, title, project_id, created_by),
          user_profiles!task_recurrences_created_by_fkey(name, email)
        `, { count: 'exact' });

      // Apply filters
      if (filter.status && filter.status.length > 0) {
        query = query.in('status', filter.status);
      }

      if (filter.frequency && filter.frequency.length > 0) {
        query = query.in('frequency', filter.frequency);
      }

      if (filter.createdBy && filter.createdBy.length > 0) {
        query = query.in('created_by', filter.createdBy);
      }

      if (filter.dateRange) {
        query = query
          .gte('created_at', filter.dateRange.start)
          .lte('created_at', filter.dateRange.end);
      }

      if (filter.hasErrors) {
        query = query.gt('failed_executions', 0);
      }

      if (filter.isOverdue) {
        query = query
          .eq('is_active', true)
          .lt('next_execution_date', new Date().toISOString());
      }

      // Always exclude archived unless specifically requested
      query = query.is('archived_at', null);

      // Apply pagination
      const offset = (page - 1) * pageSize;
      query = query.range(offset, offset + pageSize - 1);

      // Order by creation date (newest first)
      query = query.order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) {
        console.error('Error searching recurrences:', error);
        return { success: false, error: 'Failed to search recurrences' };
      }

      const recurrences = data.map(this.mapDatabaseToRecurrence);
      
      return {
        success: true,
        data: {
          recurrences,
          totalCount: count || 0,
          hasMore: (count || 0) > offset + pageSize
        }
      };
    } catch (error) {
      console.error('Error in searchRecurrences:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get detailed audit data for a specific recurrence
   */
  async getRecurrenceAuditData(recurrenceId: string): Promise<RecurrenceServiceResponse<RecurrenceAuditData>> {
    try {
      // Get recurrence with task and user details
      const { data: recurrenceData, error: recurrenceError } = await supabase
        .from('task_recurrences')
        .select(`
          *,
          tasks!inner(id, title, project_id),
          projects(name),
          user_profiles!task_recurrences_created_by_fkey(name)
        `)
        .eq('id', recurrenceId)
        .single();

      if (recurrenceError) {
        return { success: false, error: 'Recurrence not found' };
      }

      // Get recent executions
      const { data: executionsData, error: executionsError } = await supabase
        .from('task_recurrence_executions')
        .select('*')
        .eq('recurrence_id', recurrenceId)
        .order('executed_date', { ascending: false })
        .limit(20);

      if (executionsError) {
        console.error('Error fetching executions:', executionsError);
        return { success: false, error: 'Failed to fetch execution history' };
      }

      const auditData: RecurrenceAuditData = {
        recurrence: this.mapDatabaseToRecurrence(recurrenceData),
        recentExecutions: executionsData.map(this.mapDatabaseToExecution),
        taskInfo: {
          id: recurrenceData.tasks.id,
          title: recurrenceData.tasks.title,
          projectName: recurrenceData.projects?.name,
          ownerName: recurrenceData.user_profiles?.name
        }
      };

      return { success: true, data: auditData };
    } catch (error) {
      console.error('Error in getRecurrenceAuditData:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Perform bulk operations on multiple recurrences
   */
  async performBulkOperation(operation: BulkRecurrenceOperation): Promise<RecurrenceServiceResponse<BulkOperationResult>> {
    try {
      const result: BulkOperationResult = {
        successful: [],
        failed: [],
        totalProcessed: operation.recurrenceIds.length
      };

      for (const recurrenceId of operation.recurrenceIds) {
        try {
          await this.performSingleOperation(recurrenceId, operation.operation, operation.reason);
          result.successful.push(recurrenceId);
        } catch (error) {
          result.failed.push({
            id: recurrenceId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return { success: true, data: result };
    } catch (error) {
      console.error('Error in performBulkOperation:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get recurrences that may need attention (errors, overdue, etc.)
   */
  async getProblematicRecurrences(): Promise<RecurrenceServiceResponse<TaskRecurrence[]>> {
    try {
      const { data, error } = await supabase
        .from('task_recurrences')
        .select('*')
        .or('status.eq.error,failed_executions.gt.3,next_execution_date.lt.' + new Date().toISOString())
        .eq('is_active', true)
        .is('archived_at', null)
        .order('failed_executions', { ascending: false })
        .limit(100);

      if (error) {
        console.error('Error getting problematic recurrences:', error);
        return { success: false, error: 'Failed to fetch problematic recurrences' };
      }

      const recurrences = data.map(this.mapDatabaseToRecurrence);
      return { success: true, data: recurrences };
    } catch (error) {
      console.error('Error in getProblematicRecurrences:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Get execution statistics for a date range
   */
  async getExecutionStatistics(
    startDate: string,
    endDate: string
  ): Promise<RecurrenceServiceResponse<any>> {
    try {
      const { data, error } = await supabase
        .from('task_recurrence_executions')
        .select('status, executed_date, execution_duration_ms')
        .gte('executed_date', startDate)
        .lte('executed_date', endDate);

      if (error) {
        console.error('Error getting execution statistics:', error);
        return { success: false, error: 'Failed to fetch execution statistics' };
      }

      // Process statistics
      const stats = {
        totalExecutions: data.length,
        successfulExecutions: data.filter(e => e.status === 'success').length,
        failedExecutions: data.filter(e => e.status === 'failed').length,
        skippedExecutions: data.filter(e => e.status === 'skipped').length,
        averageDuration: data.reduce((sum, e) => sum + (e.execution_duration_ms || 0), 0) / data.length,
        executionsByDay: this.groupExecutionsByDay(data)
      };

      return { success: true, data: stats };
    } catch (error) {
      console.error('Error in getExecutionStatistics:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Clean up old execution records (for maintenance)
   */
  async cleanupOldExecutions(olderThanDays: number = 90): Promise<RecurrenceServiceResponse<number>> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const { data, error } = await supabase
        .from('task_recurrence_executions')
        .delete()
        .lt('executed_date', cutoffDate.toISOString());

      if (error) {
        console.error('Error cleaning up old executions:', error);
        return { success: false, error: 'Failed to cleanup old executions' };
      }

      return { success: true, data: data?.length || 0 };
    } catch (error) {
      console.error('Error in cleanupOldExecutions:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Export recurrence data for external analysis
   */
  async exportRecurrenceData(filter: RecurrenceFilter = {}): Promise<RecurrenceServiceResponse<any[]>> {
    try {
      // Get all matching recurrences without pagination
      const searchResult = await this.searchRecurrences(filter, 1, 10000);
      
      if (!searchResult.success || !searchResult.data) {
        return { success: false, error: 'Failed to fetch data for export' };
      }

      // Format data for export
      const exportData = searchResult.data.recurrences.map(rec => ({
        id: rec.id,
        name: rec.name,
        frequency: rec.frequency,
        status: rec.status,
        isActive: rec.isActive,
        totalExecutions: rec.totalExecutions,
        successfulExecutions: rec.successfulExecutions,
        failedExecutions: rec.failedExecutions,
        lastExecutionDate: rec.lastExecutionDate,
        nextExecutionDate: rec.nextExecutionDate,
        createdAt: rec.createdAt,
        createdBy: rec.createdBy
      }));

      return { success: true, data: exportData };
    } catch (error) {
      console.error('Error in exportRecurrenceData:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Perform a single operation on a recurrence
   */
  private async performSingleOperation(
    recurrenceId: string,
    operation: BulkRecurrenceOperation['operation'],
    reason?: string
  ): Promise<void> {
    const updateData: any = {};

    switch (operation) {
      case 'activate':
        updateData.is_active = true;
        updateData.status = 'active';
        break;
      case 'deactivate':
        updateData.is_active = false;
        break;
      case 'pause':
        updateData.status = 'paused';
        updateData.is_active = false;
        break;
      case 'cancel':
        updateData.status = 'cancelled';
        updateData.is_active = false;
        break;
      case 'delete':
        updateData.archived_at = new Date().toISOString();
        updateData.is_active = false;
        break;
    }

    const { error } = await supabase
      .from('task_recurrences')
      .update(updateData)
      .eq('id', recurrenceId);

    if (error) {
      throw new Error(`Failed to ${operation} recurrence: ${error.message}`);
    }
  }

  /**
   * Group executions by day for statistics
   */
  private groupExecutionsByDay(executions: any[]): Record<string, number> {
    const grouped: Record<string, number> = {};
    
    executions.forEach(execution => {
      if (execution.executed_date) {
        const day = execution.executed_date.split('T')[0];
        grouped[day] = (grouped[day] || 0) + 1;
      }
    });

    return grouped;
  }

  /**
   * Map database record to TaskRecurrence interface
   */
  private mapDatabaseToRecurrence(data: any): TaskRecurrence {
    return {
      id: data.id,
      taskId: data.task_id,
      name: data.name,
      description: data.description,
      frequency: data.frequency,
      patternConfig: data.pattern_config,
      startDate: data.start_date,
      endDate: data.end_date,
      nextExecutionDate: data.next_execution_date,
      timezone: data.timezone,
      status: data.status,
      isActive: data.is_active,
      maxExecutions: data.max_executions,
      totalExecutions: data.total_executions,
      successfulExecutions: data.successful_executions,
      failedExecutions: data.failed_executions,
      lastExecutionDate: data.last_execution_date,
      lastExecutionStatus: data.last_execution_status,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      createdBy: data.created_by,
      updatedBy: data.updated_by,
      archivedAt: data.archived_at,
      archivedBy: data.archived_by,
      version: data.version
    };
  }

  /**
   * Map database record to TaskRecurrenceExecution interface
   */
  private mapDatabaseToExecution(data: any): TaskRecurrenceExecution {
    return {
      id: data.id,
      recurrenceId: data.recurrence_id,
      scheduledDate: data.scheduled_date,
      executedDate: data.executed_date,
      status: data.status,
      createdTaskId: data.created_task_id,
      errorMessage: data.error_message,
      errorDetails: data.error_details,
      executionDurationMs: data.execution_duration_ms,
      createdAt: data.created_at,
      updatedAt: data.updated_at
    };
  }
}

export const recurrenceAuditService = new RecurrenceAuditService();
