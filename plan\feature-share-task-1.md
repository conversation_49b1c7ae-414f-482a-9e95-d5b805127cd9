---
goal: Implement Share Task Functionality with Shareable Links
version: 1.0
date_created: 2025-09-11
last_updated: 2025-09-11
owner: AI Agent
tags: [feature, task-sharing, navigation, url-handling]
---

# Introduction

This plan implements a "share task" functionality that allows users to generate shareable links for tasks, enabling direct navigation to task edit screens via URL parameters. The implementation will also fix the existing broken "View Task" link in the inbox functionality.

## 1. Requirements & Constraints

- **REQ-001**: Add "Share Task" option to three-dot menu in TaskForm and SubtaskForm components
- **REQ-002**: Generate shareable URLs using task ID as parameter (format: #tasks?taskId={taskId})
- **REQ-003**: Implement URL parameter handling to open task edit screen directly from shared links
- **REQ-004**: Fix broken "View Task" link in inbox notifications using the new navigation system
- **REQ-005**: Provide copy-to-clipboard functionality for generated share links
- **REQ-006**: Ensure shared links work for both tasks and subtasks
- **SEC-001**: Validate user permissions before opening shared tasks (existing RLS policies apply)
- **SEC-002**: Ensure shared links only work for authenticated users with proper access
- **CON-001**: Must not disrupt existing navigation patterns or task management workflows
- **CON-002**: Implementation must be modular and non-destructive to existing codebase
- **GUD-001**: Follow existing UI patterns for three-dot menus and action buttons
- **GUD-002**: Use consistent styling with existing clone task functionality
- **PAT-001**: Leverage existing hash-based navigation system in SupabaseSidebar
- **PAT-002**: Follow existing URL parameter patterns used in inbox navigation

## 2. Implementation Steps

### Phase 1: Core URL Navigation System
1. **Create URL parameter handling utility** (`src/utils/navigationUtils.ts`)
   - Parse URL parameters for taskId
   - Generate shareable URLs with proper format
   - Handle navigation to task edit screens

2. **Enhance SupabaseSidebar navigation** (`src/components/SupabaseSidebar.tsx`)
   - Add URL parameter detection on component mount
   - Implement automatic task opening when taskId parameter is present
   - Ensure proper view mode switching (tasks section activation)

### Phase 2: Share Task UI Components
3. **Add Share Task option to TaskForm** (`src/components/TaskForm.tsx`)
   - Add Share Task button to existing three-dot menu
   - Implement share link generation and clipboard copy
   - Add success notification for copy action

4. **Add Share Task option to SubtaskForm** (`src/components/SubtaskForm.tsx`)
   - Mirror TaskForm implementation for consistency
   - Handle subtask-specific sharing (parent task with subtask focus)

### Phase 3: Enhanced Task Opening System
5. **Create task opening service** (`src/services/taskNavigationService.ts`)
   - Centralized task opening logic
   - Handle both direct task opening and subtask opening
   - Integrate with existing task management workflows

6. **Update store with navigation actions** (`src/store/useSupabaseStore.ts`)
   - Add openTaskById action for programmatic task opening
   - Add URL parameter handling integration
   - Ensure proper state management for opened tasks

### Phase 4: Fix Inbox Navigation
7. **Fix inbox "View Task" functionality** (`src/components/Inbox.tsx`)
   - Replace broken navigation with new task opening system
   - Use centralized navigation service
   - Ensure notifications are marked as read on task opening

## 3. Alternatives

- **ALT-001**: Use React Router for URL handling - Rejected due to existing hash-based navigation system
- **ALT-002**: Implement modal-based task sharing - Rejected in favor of direct URL sharing for better UX
- **ALT-003**: Add sharing to task list view only - Rejected to maintain consistency across all task interfaces

## 4. Dependencies

- **DEP-001**: Existing hash-based navigation system in SupabaseSidebar
- **DEP-002**: Current three-dot menu implementation in TaskForm/SubtaskForm
- **DEP-003**: Existing task management store actions and state
- **DEP-004**: Browser Clipboard API for copy functionality
- **DEP-005**: Existing notification system for user feedback

## 5. Files

- **FILE-001**: `src/utils/navigationUtils.ts` - New utility for URL handling and navigation
- **FILE-002**: `src/services/taskNavigationService.ts` - New service for centralized task opening
- **FILE-003**: `src/components/TaskForm.tsx` - Add share functionality to three-dot menu
- **FILE-004**: `src/components/SubtaskForm.tsx` - Add share functionality to three-dot menu
- **FILE-005**: `src/components/SupabaseSidebar.tsx` - Enhance URL parameter handling
- **FILE-006**: `src/store/useSupabaseStore.ts` - Add navigation actions and state
- **FILE-007**: `src/components/Inbox.tsx` - Fix broken task navigation
- **FILE-008**: `src/components/NotificationItem.tsx` - Update to use new navigation system

## 6. Testing

- **TEST-001**: Verify share link generation creates correct URL format
- **TEST-002**: Test clipboard copy functionality across different browsers
- **TEST-003**: Validate task opening from shared URLs with proper permissions
- **TEST-004**: Ensure inbox "View Task" links work correctly
- **TEST-005**: Test subtask sharing and opening functionality
- **TEST-006**: Verify navigation doesn't break existing workflows
- **TEST-007**: Test with different user permission levels

## 7. Risks & Assumptions

- **RISK-001**: Browser clipboard API may not work in all environments (fallback needed)
- **RISK-002**: URL parameter handling might conflict with existing navigation
- **ASSUMPTION-001**: Users will primarily share links within the same organization
- **ASSUMPTION-002**: Existing RLS policies provide sufficient security for shared links
- **ASSUMPTION-003**: Hash-based navigation system will remain stable

## 8. Related Specifications / Further Reading

- Existing inbox functionality documentation: `docs/inbox-feature-testing-guide.md`
- Clone task implementation: `src/services/cloneService.ts`
- Current navigation patterns: `src/components/SupabaseSidebar.tsx`
- Task management store: `src/store/useSupabaseStore.ts`
