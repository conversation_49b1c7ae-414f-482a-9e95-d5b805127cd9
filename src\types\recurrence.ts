/**
 * Type definitions for recurring tasks functionality
 * Supports flexible recurrence patterns with comprehensive audit trail
 */

// Enums matching database types
export type RecurrenceStatus = 'active' | 'paused' | 'completed' | 'cancelled' | 'error';
export type RecurrenceFrequency = 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
export type ExecutionStatus = 'pending' | 'success' | 'failed' | 'skipped';

// Base recurrence pattern configurations
export interface DailyPattern {
  type: 'daily';
  interval: number; // Every N days
  weekdaysOnly?: boolean; // Skip weekends
}

export interface WeeklyPattern {
  type: 'weekly';
  interval: number; // Every N weeks
  daysOfWeek: number[]; // 0 = Sunday, 1 = Monday, etc.
}

export interface MonthlyPattern {
  type: 'monthly';
  interval: number; // Every N months
  dayOfMonth?: number; // Specific day of month (1-31)
  weekOfMonth?: number; // Which week (1-4, -1 for last)
  dayOfWeek?: number; // Day of week for week-based monthly
}

export interface YearlyPattern {
  type: 'yearly';
  interval: number; // Every N years
  month: number; // Month (1-12)
  dayOfMonth?: number; // Specific day
  weekOfMonth?: number; // Which week
  dayOfWeek?: number; // Day of week for week-based yearly
}

export interface CustomPattern {
  type: 'custom';
  cronExpression?: string; // Cron-like expression for complex patterns
  customRules?: {
    description: string;
    nextExecutionCalculator: string; // Serialized function or rule
  };
}

// Union type for all pattern configurations
export type RecurrencePatternConfig = 
  | DailyPattern 
  | WeeklyPattern 
  | MonthlyPattern 
  | YearlyPattern 
  | CustomPattern;

// Main recurrence configuration interface
export interface TaskRecurrence {
  id: string;
  taskId: string;
  name: string;
  description?: string;
  
  // Pattern configuration
  frequency: RecurrenceFrequency;
  patternConfig: RecurrencePatternConfig;
  
  // Scheduling
  startDate: string; // ISO date string
  endDate?: string; // Optional end date
  nextExecutionDate?: string; // ISO datetime string
  timezone: string;
  
  // Control and limits
  status: RecurrenceStatus;
  isActive: boolean;
  maxExecutions?: number;
  
  // Statistics (for admin reporting)
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  lastExecutionDate?: string;
  lastExecutionStatus?: string;
  
  // Audit trail
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: string;
  
  // Soft delete
  archivedAt?: string;
  archivedBy?: string;
  
  // Version control
  version: number;
}

// Execution tracking interface
export interface TaskRecurrenceExecution {
  id: string;
  recurrenceId: string;
  scheduledDate: string;
  executedDate?: string;
  status: ExecutionStatus;
  createdTaskId?: string;
  errorMessage?: string;
  errorDetails?: Record<string, any>;
  executionDurationMs?: number;
  createdAt: string;
  updatedAt: string;
}

// Input types for creating/updating recurrences
export interface CreateRecurrenceInput {
  taskId: string;
  name: string;
  description?: string;
  frequency: RecurrenceFrequency;
  patternConfig: RecurrencePatternConfig;
  startDate: string;
  endDate?: string;
  timezone?: string;
  maxExecutions?: number;
}

export interface UpdateRecurrenceInput {
  name?: string;
  description?: string;
  frequency?: RecurrenceFrequency;
  patternConfig?: RecurrencePatternConfig;
  startDate?: string;
  endDate?: string;
  timezone?: string;
  status?: RecurrenceStatus;
  isActive?: boolean;
  maxExecutions?: number;
}

// Admin reporting interfaces
export interface RecurrenceStatistics {
  totalActiveRecurrences: number;
  totalExecutionsToday: number;
  totalFailedExecutionsToday: number;
  avgExecutionDurationMs: number;
  mostFrequentFrequency: string;
}

export interface RecurrenceAuditData {
  recurrence: TaskRecurrence;
  recentExecutions: TaskRecurrenceExecution[];
  taskInfo: {
    id: string;
    title: string;
    projectName?: string;
    ownerName?: string;
  };
}

// Utility interfaces for UI components
export interface RecurrencePreview {
  nextExecutions: string[]; // Next 5 execution dates
  description: string; // Human-readable description
  isValid: boolean;
  validationErrors?: string[];
}

export interface RecurrenceFormData {
  name: string;
  description: string;
  frequency: RecurrenceFrequency;
  patternConfig: Partial<RecurrencePatternConfig>;
  startDate: string;
  endDate: string;
  timezone: string;
  maxExecutions: string;
}

// Service response types
export interface RecurrenceServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  validationErrors?: Record<string, string>;
}

// Bulk operations for admin functionality
export interface BulkRecurrenceOperation {
  recurrenceIds: string[];
  operation: 'activate' | 'deactivate' | 'pause' | 'cancel' | 'delete';
  reason?: string;
}

export interface BulkOperationResult {
  successful: string[];
  failed: Array<{
    id: string;
    error: string;
  }>;
  totalProcessed: number;
}

// Filter and search interfaces for admin dashboard
export interface RecurrenceFilter {
  status?: RecurrenceStatus[];
  frequency?: RecurrenceFrequency[];
  createdBy?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  hasErrors?: boolean;
  isOverdue?: boolean;
}

export interface RecurrenceSearchResult {
  recurrences: TaskRecurrence[];
  totalCount: number;
  hasMore: boolean;
}

// Validation and utility types
export interface ValidationRule {
  field: keyof TaskRecurrence;
  rule: string;
  message: string;
}

export interface RecurrenceValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>;
}

// Event types for automation integration
export interface RecurrenceEvent {
  type: 'execution_scheduled' | 'execution_completed' | 'execution_failed' | 'recurrence_created' | 'recurrence_updated' | 'recurrence_deleted';
  recurrenceId: string;
  taskId: string;
  timestamp: string;
  data: Record<string, any>;
}

// Export utility type for pattern type guards
export type PatternType = RecurrencePatternConfig['type'];

// Helper type for pattern-specific configurations
export type PatternConfigForType<T extends PatternType> = 
  T extends 'daily' ? DailyPattern :
  T extends 'weekly' ? WeeklyPattern :
  T extends 'monthly' ? MonthlyPattern :
  T extends 'yearly' ? YearlyPattern :
  T extends 'custom' ? CustomPattern :
  never;
