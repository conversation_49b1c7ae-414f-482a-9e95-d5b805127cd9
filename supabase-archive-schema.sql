-- Archive System Database Schema Updates
-- This file contains all the database changes needed for the archive functionality
--
-- FEATURES:
-- - Soft-delete functionality for folders, projects, and tasks
-- - Cascade operations (archiving a folder archives all its contents)
-- - Original location tracking for proper restoration
-- - Admin-only restore capabilities
-- - Automatic cleanup of items older than 1 week
-- - Filtered queries to exclude archived items from normal operations
--
-- USAGE:
-- Run this file in your Supabase SQL editor after setting up the main schema
-- This adds archive functionality to an existing project management database
--
-- SECURITY:
-- - Only authenticated users can archive their own items
-- - Only admin users can view and restore archived items
-- - Archived items are automatically excluded from normal queries

-- Add archive columns to folders table
ALTER TABLE folders ADD COLUMN IF NOT EXISTS archived_at TIMESTAMPTZ;
ALTER TABLE folders ADD COLUMN IF NOT EXISTS archived_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;
ALTER TABLE folders ADD COLUMN IF NOT EXISTS original_parent_id UUID;

-- Add archive columns to projects table
ALTER TABLE projects ADD COLUMN IF NOT EXISTS archived_at TIMESTAMPTZ;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS archived_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;
ALTER TABLE projects ADD COLUMN IF NOT EXISTS original_folder_id UUID;

-- Add archive columns to tasks table
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS archived_at TIMESTAMPTZ;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS archived_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS original_project_id UUID;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS original_folder_id UUID;

-- Function to archive a folder and all its contents
CREATE OR REPLACE FUNCTION archive_folder_cascade(p_folder_id UUID, p_archived_by_user UUID)
RETURNS VOID AS $$
DECLARE
    subfolder_id UUID;
    project_id UUID;
    task_id UUID;
BEGIN
    -- Archive the folder itself
    UPDATE folders
    SET archived_at = NOW(),
        archived_by = p_archived_by_user,
        original_parent_id = parent_id
    WHERE id = p_folder_id AND archived_at IS NULL;

    -- Archive all subfolders recursively
    FOR subfolder_id IN
        SELECT id FROM folders
        WHERE parent_id = p_folder_id AND archived_at IS NULL
    LOOP
        PERFORM archive_folder_cascade(subfolder_id, p_archived_by_user);
    END LOOP;

    -- Archive all projects in this folder
    FOR project_id IN
        SELECT id FROM projects
        WHERE folder_id = p_folder_id AND archived_at IS NULL
    LOOP
        PERFORM archive_project_cascade(project_id, p_archived_by_user);
    END LOOP;

    -- Archive all tasks directly in this folder (not in projects)
    UPDATE tasks
    SET archived_at = NOW(),
        archived_by = p_archived_by_user,
        original_folder_id = p_folder_id
    WHERE folder_id = p_folder_id AND project_id IS NULL AND archived_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to archive a project and all its tasks
CREATE OR REPLACE FUNCTION archive_project_cascade(p_project_id UUID, p_archived_by_user UUID)
RETURNS VOID AS $$
BEGIN
    -- Archive the project itself
    UPDATE projects
    SET archived_at = NOW(),
        archived_by = p_archived_by_user,
        original_folder_id = folder_id
    WHERE id = p_project_id AND archived_at IS NULL;

    -- Archive all tasks in this project
    UPDATE tasks
    SET archived_at = NOW(),
        archived_by = p_archived_by_user,
        original_project_id = project_id,
        original_folder_id = folder_id
    WHERE project_id = p_project_id AND archived_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to restore a folder and all its contents
CREATE OR REPLACE FUNCTION restore_folder_cascade(p_folder_id UUID)
RETURNS VOID AS $$
DECLARE
    subfolder_id UUID;
    project_id UUID;
BEGIN
    -- Restore the folder itself
    UPDATE folders
    SET archived_at = NULL,
        archived_by = NULL,
        parent_id = original_parent_id,
        original_parent_id = NULL
    WHERE id = p_folder_id AND archived_at IS NOT NULL;

    -- Restore all subfolders
    FOR subfolder_id IN
        SELECT id FROM folders
        WHERE original_parent_id = p_folder_id AND archived_at IS NOT NULL
    LOOP
        PERFORM restore_folder_cascade(subfolder_id);
    END LOOP;

    -- Restore all projects that were in this folder
    FOR project_id IN
        SELECT id FROM projects
        WHERE original_folder_id = p_folder_id AND archived_at IS NOT NULL
    LOOP
        PERFORM restore_project_cascade(project_id);
    END LOOP;

    -- Restore all tasks that were directly in this folder
    UPDATE tasks
    SET archived_at = NULL,
        archived_by = NULL,
        folder_id = original_folder_id,
        original_folder_id = NULL,
        original_project_id = NULL
    WHERE original_folder_id = p_folder_id AND original_project_id IS NULL AND archived_at IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to restore a project and all its tasks
CREATE OR REPLACE FUNCTION restore_project_cascade(p_project_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Restore the project itself
    UPDATE projects
    SET archived_at = NULL,
        archived_by = NULL,
        folder_id = original_folder_id,
        original_folder_id = NULL
    WHERE id = p_project_id AND archived_at IS NOT NULL;

    -- Restore all tasks in this project
    UPDATE tasks
    SET archived_at = NULL,
        archived_by = NULL,
        project_id = original_project_id,
        folder_id = original_folder_id,
        original_project_id = NULL,
        original_folder_id = NULL
    WHERE original_project_id = p_project_id AND archived_at IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to permanently delete expired archived items (older than 1 week)
CREATE OR REPLACE FUNCTION cleanup_expired_archives()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    temp_count INTEGER;
BEGIN
    -- Delete expired tasks
    DELETE FROM tasks 
    WHERE archived_at IS NOT NULL 
    AND archived_at < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Delete expired projects
    DELETE FROM projects 
    WHERE archived_at IS NOT NULL 
    AND archived_at < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    -- Delete expired folders
    DELETE FROM folders 
    WHERE archived_at IS NOT NULL 
    AND archived_at < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS temp_count = ROW_COUNT;
    deleted_count := deleted_count + temp_count;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policies to handle archived items

-- Update folders policies to exclude archived items from normal view
DROP POLICY IF EXISTS "Users can view all folders" ON folders;
CREATE POLICY "Users can view non-archived folders" ON folders 
FOR SELECT USING (archived_at IS NULL);

-- Create policy for admins to view archived folders
CREATE POLICY "Admins can view archived folders" ON folders 
FOR SELECT USING (
  archived_at IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Update projects policies to exclude archived items from normal view
DROP POLICY IF EXISTS "Users can view all projects" ON projects;
CREATE POLICY "Users can view non-archived projects" ON projects 
FOR SELECT USING (archived_at IS NULL);

-- Create policy for admins to view archived projects
CREATE POLICY "Admins can view archived projects" ON projects 
FOR SELECT USING (
  archived_at IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Update tasks policies to exclude archived items from normal view
DROP POLICY IF EXISTS "Users can view tasks they have access to" ON tasks;
CREATE POLICY "Users can view non-archived tasks they have access to" ON tasks 
FOR SELECT USING (
  archived_at IS NULL AND (
    created_by = auth.uid() OR
    assigned_user_id = auth.uid() OR
    owner_id = auth.uid() OR
    auth.uid()::text = ANY(assigned_users) OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      WHERE up.id = auth.uid() AND (
        up.role = 'admin' OR
        up.group_id::text = ANY(assigned_groups)
      )
    )
  )
);

-- Create policy for admins to view archived tasks
CREATE POLICY "Admins can view archived tasks" ON tasks 
FOR SELECT USING (
  archived_at IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);
