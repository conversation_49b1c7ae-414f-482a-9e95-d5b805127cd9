/**
 * Utility functions for recurrence pattern calculations and validation
 * Provides helper functions for date calculations, pattern validation, and preview generation
 */

import {
  RecurrencePatternConfig,
  DailyPattern,
  WeeklyPattern,
  MonthlyPattern,
  YearlyPattern,
  RecurrencePreview,
  RecurrenceValidationResult
} from '../types/recurrence';

/**
 * Generate a human-readable description of a recurrence pattern
 */
export function generateRecurrenceDescription(pattern: RecurrencePatternConfig): string {
  switch (pattern.type) {
    case 'daily':
      if (pattern.interval === 1) {
        return pattern.weekdaysOnly ? 'Every weekday' : 'Every day';
      }
      return `Every ${pattern.interval} days${pattern.weekdaysOnly ? ' (weekdays only)' : ''}`;

    case 'weekly':
      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      if (pattern.interval === 1 && pattern.daysOfWeek.length === 1) {
        return `Every ${dayNames[pattern.daysOfWeek[0]]}`;
      }
      if (pattern.interval === 1 && pattern.daysOfWeek.length > 1) {
        const days = pattern.daysOfWeek.map(d => dayNames[d]).join(', ');
        return `Every week on ${days}`;
      }
      const weeklyDays = pattern.daysOfWeek.map(d => dayNames[d]).join(', ');
      return `Every ${pattern.interval} weeks on ${weeklyDays}`;

    case 'monthly':
      if (pattern.dayOfMonth) {
        const suffix = getOrdinalSuffix(pattern.dayOfMonth);
        if (pattern.interval === 1) {
          return `Every month on the ${pattern.dayOfMonth}${suffix}`;
        }
        return `Every ${pattern.interval} months on the ${pattern.dayOfMonth}${suffix}`;
      }
      if (pattern.weekOfMonth && pattern.dayOfWeek !== undefined) {
        const dayName = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][pattern.dayOfWeek];
        const weekDesc = pattern.weekOfMonth === -1 ? 'last' : getOrdinalSuffix(pattern.weekOfMonth);
        if (pattern.interval === 1) {
          return `Every month on the ${weekDesc} ${dayName}`;
        }
        return `Every ${pattern.interval} months on the ${weekDesc} ${dayName}`;
      }
      return `Every ${pattern.interval || 1} month${pattern.interval !== 1 ? 's' : ''}`;

    case 'yearly':
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      const monthName = pattern.month ? monthNames[pattern.month - 1] : '';
      
      if (pattern.dayOfMonth && pattern.month) {
        const suffix = getOrdinalSuffix(pattern.dayOfMonth);
        return `Every year on ${monthName} ${pattern.dayOfMonth}${suffix}`;
      }
      if (pattern.weekOfMonth && pattern.dayOfWeek !== undefined && pattern.month) {
        const dayName = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][pattern.dayOfWeek];
        const weekDesc = pattern.weekOfMonth === -1 ? 'last' : getOrdinalSuffix(pattern.weekOfMonth);
        return `Every year on the ${weekDesc} ${dayName} of ${monthName}`;
      }
      return `Every ${pattern.interval || 1} year${pattern.interval !== 1 ? 's' : ''}`;

    case 'custom':
      return pattern.customRules?.description || 'Custom pattern';

    default:
      return 'Unknown pattern';
  }
}

/**
 * Generate preview of next execution dates
 */
export function generateRecurrencePreview(
  pattern: RecurrencePatternConfig,
  startDate: string,
  timezone: string = 'UTC',
  count: number = 5
): RecurrencePreview {
  try {
    const validation = validateRecurrencePattern(pattern);
    if (!validation.isValid) {
      return {
        nextExecutions: [],
        description: generateRecurrenceDescription(pattern),
        isValid: false,
        validationErrors: Object.values(validation.errors)
      };
    }

    const nextExecutions: string[] = [];
    let currentDate = new Date(startDate);
    
    for (let i = 0; i < count; i++) {
      const nextDate = calculateNextExecutionFromDate(pattern, currentDate.toISOString(), timezone);
      nextExecutions.push(nextDate);
      currentDate = new Date(nextDate);
    }

    return {
      nextExecutions,
      description: generateRecurrenceDescription(pattern),
      isValid: true
    };
  } catch (error) {
    return {
      nextExecutions: [],
      description: generateRecurrenceDescription(pattern),
      isValid: false,
      validationErrors: ['Error generating preview: ' + (error instanceof Error ? error.message : 'Unknown error')]
    };
  }
}

/**
 * Calculate next execution date from a given date
 */
export function calculateNextExecutionFromDate(
  pattern: RecurrencePatternConfig,
  fromDate: string,
  timezone: string = 'UTC'
): string {
  const baseDate = new Date(fromDate);
  let nextDate: Date;

  switch (pattern.type) {
    case 'daily':
      nextDate = calculateNextDaily(baseDate, pattern);
      break;
    case 'weekly':
      nextDate = calculateNextWeekly(baseDate, pattern);
      break;
    case 'monthly':
      nextDate = calculateNextMonthly(baseDate, pattern);
      break;
    case 'yearly':
      nextDate = calculateNextYearly(baseDate, pattern);
      break;
    case 'custom':
      // For custom patterns, implement cron parsing or custom logic
      nextDate = new Date(baseDate);
      nextDate.setDate(nextDate.getDate() + 1);
      break;
    default:
      nextDate = new Date(baseDate);
      nextDate.setDate(nextDate.getDate() + 1);
  }

  return nextDate.toISOString();
}

/**
 * Calculate next daily execution
 */
function calculateNextDaily(baseDate: Date, pattern: DailyPattern): Date {
  const nextDate = new Date(baseDate);
  nextDate.setDate(nextDate.getDate() + (pattern.interval || 1));
  
  if (pattern.weekdaysOnly) {
    // Skip weekends
    while (nextDate.getDay() === 0 || nextDate.getDay() === 6) {
      nextDate.setDate(nextDate.getDate() + 1);
    }
  }
  
  return nextDate;
}

/**
 * Calculate next weekly execution
 */
function calculateNextWeekly(baseDate: Date, pattern: WeeklyPattern): Date {
  const nextDate = new Date(baseDate);
  const currentDay = nextDate.getDay();
  
  if (pattern.daysOfWeek && pattern.daysOfWeek.length > 0) {
    const sortedDays = [...pattern.daysOfWeek].sort();
    
    // Find next target day in current week
    let nextTargetDay = sortedDays.find(day => day > currentDay);
    
    if (nextTargetDay !== undefined) {
      // Next occurrence is in the current week
      const daysToAdd = nextTargetDay - currentDay;
      nextDate.setDate(nextDate.getDate() + daysToAdd);
    } else {
      // Next occurrence is in the next interval
      const weeksToAdd = pattern.interval || 1;
      nextDate.setDate(nextDate.getDate() + (7 * weeksToAdd));
      
      // Set to first target day of that week
      const targetDay = sortedDays[0];
      const newCurrentDay = nextDate.getDay();
      const daysToAdd = (targetDay - newCurrentDay + 7) % 7;
      nextDate.setDate(nextDate.getDate() + daysToAdd);
    }
  } else {
    // No specific days, just add weeks
    nextDate.setDate(nextDate.getDate() + (7 * (pattern.interval || 1)));
  }
  
  return nextDate;
}

/**
 * Calculate next monthly execution
 */
function calculateNextMonthly(baseDate: Date, pattern: MonthlyPattern): Date {
  const nextDate = new Date(baseDate);
  nextDate.setMonth(nextDate.getMonth() + (pattern.interval || 1));
  
  if (pattern.dayOfMonth) {
    nextDate.setDate(pattern.dayOfMonth);
    
    // Handle months with fewer days (e.g., February 30th -> February 28th)
    if (nextDate.getDate() !== pattern.dayOfMonth) {
      nextDate.setDate(0); // Last day of previous month
    }
  } else if (pattern.weekOfMonth && pattern.dayOfWeek !== undefined) {
    return calculateWeekBasedDate(nextDate, pattern.weekOfMonth, pattern.dayOfWeek);
  }
  
  return nextDate;
}

/**
 * Calculate next yearly execution
 */
function calculateNextYearly(baseDate: Date, pattern: YearlyPattern): Date {
  const nextDate = new Date(baseDate);
  nextDate.setFullYear(nextDate.getFullYear() + (pattern.interval || 1));
  
  if (pattern.month) {
    nextDate.setMonth(pattern.month - 1); // Month is 1-based in pattern
  }
  
  if (pattern.dayOfMonth) {
    nextDate.setDate(pattern.dayOfMonth);
  } else if (pattern.weekOfMonth && pattern.dayOfWeek !== undefined) {
    return calculateWeekBasedDate(nextDate, pattern.weekOfMonth, pattern.dayOfWeek);
  }
  
  return nextDate;
}

/**
 * Calculate week-based date (e.g., "2nd Tuesday of month")
 */
function calculateWeekBasedDate(baseDate: Date, weekOfMonth: number, dayOfWeek: number): Date {
  const result = new Date(baseDate);
  result.setDate(1); // Start at first day of month
  
  // Find first occurrence of target day
  const firstDayOfMonth = result.getDay();
  const daysToAdd = (dayOfWeek - firstDayOfMonth + 7) % 7;
  result.setDate(1 + daysToAdd);
  
  if (weekOfMonth === -1) {
    // Last occurrence of the day in the month
    result.setMonth(result.getMonth() + 1, 0); // Last day of month
    const lastDayOfMonth = result.getDay();
    const daysToSubtract = (lastDayOfMonth - dayOfWeek + 7) % 7;
    result.setDate(result.getDate() - daysToSubtract);
  } else {
    // Specific week occurrence
    result.setDate(result.getDate() + (7 * (weekOfMonth - 1)));
    
    // Check if we've gone past the end of the month
    if (result.getMonth() !== baseDate.getMonth()) {
      // Move to last valid occurrence
      result.setDate(result.getDate() - 7);
    }
  }
  
  return result;
}

/**
 * Validate a recurrence pattern
 */
export function validateRecurrencePattern(pattern: RecurrencePatternConfig): RecurrenceValidationResult {
  const errors: Record<string, string> = {};
  const warnings: Record<string, string> = {};

  switch (pattern.type) {
    case 'daily':
      if (!pattern.interval || pattern.interval < 1 || pattern.interval > 365) {
        errors.interval = 'Daily interval must be between 1 and 365 days';
      }
      break;

    case 'weekly':
      if (!pattern.interval || pattern.interval < 1 || pattern.interval > 52) {
        errors.interval = 'Weekly interval must be between 1 and 52 weeks';
      }
      if (!pattern.daysOfWeek || pattern.daysOfWeek.length === 0) {
        errors.daysOfWeek = 'At least one day of week must be selected';
      } else if (pattern.daysOfWeek.some(day => day < 0 || day > 6)) {
        errors.daysOfWeek = 'Days of week must be between 0 (Sunday) and 6 (Saturday)';
      }
      break;

    case 'monthly':
      if (!pattern.interval || pattern.interval < 1 || pattern.interval > 12) {
        errors.interval = 'Monthly interval must be between 1 and 12 months';
      }
      if (pattern.dayOfMonth && (pattern.dayOfMonth < 1 || pattern.dayOfMonth > 31)) {
        errors.dayOfMonth = 'Day of month must be between 1 and 31';
      }
      if (pattern.weekOfMonth && (pattern.weekOfMonth < -1 || pattern.weekOfMonth === 0 || pattern.weekOfMonth > 4)) {
        errors.weekOfMonth = 'Week of month must be 1-4 or -1 for last week';
      }
      if (pattern.weekOfMonth && pattern.dayOfWeek === undefined) {
        errors.dayOfWeek = 'Day of week is required when week of month is specified';
      }
      break;

    case 'yearly':
      if (!pattern.interval || pattern.interval < 1 || pattern.interval > 10) {
        errors.interval = 'Yearly interval must be between 1 and 10 years';
      }
      if (pattern.month && (pattern.month < 1 || pattern.month > 12)) {
        errors.month = 'Month must be between 1 and 12';
      }
      if (pattern.dayOfMonth && (pattern.dayOfMonth < 1 || pattern.dayOfMonth > 31)) {
        errors.dayOfMonth = 'Day of month must be between 1 and 31';
      }
      break;

    case 'custom':
      if (pattern.cronExpression) {
        // Basic cron validation (simplified)
        const cronParts = pattern.cronExpression.split(' ');
        if (cronParts.length !== 5) {
          errors.cronExpression = 'Cron expression must have 5 parts (minute hour day month dayOfWeek)';
        }
      }
      break;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings
  };
}

/**
 * Get ordinal suffix for numbers (1st, 2nd, 3rd, etc.)
 */
function getOrdinalSuffix(num: number): string {
  const j = num % 10;
  const k = num % 100;
  
  if (j === 1 && k !== 11) return 'st';
  if (j === 2 && k !== 12) return 'nd';
  if (j === 3 && k !== 13) return 'rd';
  return 'th';
}

/**
 * Check if a date matches a recurrence pattern
 */
export function doesDateMatchPattern(
  date: Date,
  pattern: RecurrencePatternConfig,
  startDate: Date
): boolean {
  const daysDiff = Math.floor((date.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  
  switch (pattern.type) {
    case 'daily':
      if (pattern.weekdaysOnly) {
        const dayOfWeek = date.getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) return false;
      }
      return daysDiff >= 0 && daysDiff % (pattern.interval || 1) === 0;
      
    case 'weekly':
      if (!pattern.daysOfWeek.includes(date.getDay())) return false;
      const weeksDiff = Math.floor(daysDiff / 7);
      return weeksDiff >= 0 && weeksDiff % (pattern.interval || 1) === 0;
      
    case 'monthly':
      // Simplified monthly check
      return date.getDate() === (pattern.dayOfMonth || startDate.getDate());
      
    case 'yearly':
      return date.getMonth() === (pattern.month ? pattern.month - 1 : startDate.getMonth()) &&
             date.getDate() === (pattern.dayOfMonth || startDate.getDate());
             
    default:
      return false;
  }
}

/**
 * Format date for display in user's timezone
 */
export function formatDateForTimezone(date: string, timezone: string = 'UTC'): string {
  try {
    return new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  } catch (error) {
    return new Date(date).toLocaleString();
  }
}
