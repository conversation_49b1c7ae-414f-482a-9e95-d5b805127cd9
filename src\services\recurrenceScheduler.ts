/**
 * RecurrenceScheduler - Automated scheduling engine for recurring tasks
 * Handles execution timing, task cloning, and error recovery
 */

import { recurrenceService } from './recurrenceService';
import { CloneService } from './cloneService';
import { TaskRecurrence, RecurrencePatternConfig } from '../types/recurrence';
import { supabase } from '../lib/supabase';

class RecurrenceScheduler {
  private isRunning = false;
  private intervalId: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL_MS = 60000; // Check every minute

  /**
   * Start the scheduler
   */
  start(): void {
    if (this.isRunning) {
      console.log('RecurrenceScheduler is already running');
      return;
    }

    console.log('Starting RecurrenceScheduler...');
    this.isRunning = true;
    
    // Run immediately, then on interval
    this.processRecurrences();
    this.intervalId = setInterval(() => {
      this.processRecurrences();
    }, this.CHECK_INTERVAL_MS);
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('RecurrenceScheduler is not running');
      return;
    }

    console.log('Stopping RecurrenceScheduler...');
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Process all recurrences due for execution
   */
  private async processRecurrences(): Promise<void> {
    try {
      console.log('🔄 Processing recurring tasks...');
      
      const result = await recurrenceService.getRecurrencesDueForExecution();
      if (!result.success || !result.data) {
        console.error('Failed to fetch due recurrences:', result.error);
        return;
      }

      const dueRecurrences = result.data;
      console.log(`Found ${dueRecurrences.length} recurrences due for execution`);

      // Process each recurrence
      for (const recurrence of dueRecurrences) {
        await this.executeRecurrence(recurrence);
      }

    } catch (error) {
      console.error('Error processing recurrences:', error);
    }
  }

  /**
   * Execute a single recurrence
   */
  private async executeRecurrence(recurrence: TaskRecurrence): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🚀 Executing recurrence: ${recurrence.name} (${recurrence.id})`);

      // Check if we've reached max executions
      if (recurrence.maxExecutions && recurrence.totalExecutions >= recurrence.maxExecutions) {
        console.log(`Recurrence ${recurrence.id} has reached max executions (${recurrence.maxExecutions})`);
        await this.completeRecurrence(recurrence.id);
        return;
      }

      // Check if we've passed the end date
      if (recurrence.endDate && new Date() > new Date(recurrence.endDate)) {
        console.log(`Recurrence ${recurrence.id} has passed end date`);
        await this.completeRecurrence(recurrence.id);
        return;
      }

      // Clone the task
      const cloneResult = await CloneService.cloneTask(recurrence.taskId, {
        includeSubtasks: true,
        includeDependencies: false, // Don't clone dependencies for recurring tasks
        includeComments: false,
        includeHistory: false
      });

      if (!cloneResult.success || !cloneResult.data) {
        throw new Error(cloneResult.error || 'Failed to clone task');
      }

      const clonedTask = cloneResult.data;
      const executionDuration = Date.now() - startTime;

      // Record successful execution
      await recurrenceService.recordExecution(
        recurrence.id,
        'success',
        clonedTask.id,
        undefined,
        executionDuration
      );

      // Update next execution date
      await this.updateNextExecutionDate(recurrence);

      console.log(`✅ Successfully executed recurrence ${recurrence.id}, created task ${clonedTask.id}`);

    } catch (error) {
      const executionDuration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`❌ Failed to execute recurrence ${recurrence.id}:`, errorMessage);

      // Record failed execution
      await recurrenceService.recordExecution(
        recurrence.id,
        'failed',
        undefined,
        errorMessage,
        executionDuration
      );

      // Check if we should pause the recurrence after multiple failures
      await this.handleExecutionFailure(recurrence);
    }
  }

  /**
   * Update the next execution date for a recurrence
   */
  private async updateNextExecutionDate(recurrence: TaskRecurrence): Promise<void> {
    try {
      const nextDate = this.calculateNextExecutionDate(
        recurrence.patternConfig,
        recurrence.lastExecutionDate || recurrence.nextExecutionDate || new Date().toISOString(),
        recurrence.timezone
      );

      await supabase.rpc('update_next_execution_date', {
        recurrence_uuid: recurrence.id
      });

      // Also update with our calculated date if the DB function doesn't handle complex patterns
      await supabase
        .from('task_recurrences')
        .update({ next_execution_date: nextDate })
        .eq('id', recurrence.id);

    } catch (error) {
      console.error(`Error updating next execution date for recurrence ${recurrence.id}:`, error);
    }
  }

  /**
   * Calculate the next execution date based on pattern configuration
   */
  private calculateNextExecutionDate(
    patternConfig: RecurrencePatternConfig,
    lastExecution: string,
    timezone: string = 'UTC'
  ): string {
    const lastDate = new Date(lastExecution);
    let nextDate: Date;

    switch (patternConfig.type) {
      case 'daily':
        nextDate = new Date(lastDate);
        nextDate.setDate(nextDate.getDate() + (patternConfig.interval || 1));
        
        // Handle weekdays only
        if (patternConfig.weekdaysOnly) {
          while (nextDate.getDay() === 0 || nextDate.getDay() === 6) {
            nextDate.setDate(nextDate.getDate() + 1);
          }
        }
        break;

      case 'weekly':
        nextDate = new Date(lastDate);
        nextDate.setDate(nextDate.getDate() + (7 * (patternConfig.interval || 1)));
        
        // Handle specific days of week
        if (patternConfig.daysOfWeek && patternConfig.daysOfWeek.length > 0) {
          const targetDays = patternConfig.daysOfWeek.sort();
          const currentDay = nextDate.getDay();
          
          // Find next target day
          let nextTargetDay = targetDays.find(day => day > currentDay);
          if (!nextTargetDay) {
            nextTargetDay = targetDays[0];
            nextDate.setDate(nextDate.getDate() + 7); // Move to next week
          }
          
          const daysToAdd = (nextTargetDay - currentDay + 7) % 7;
          nextDate.setDate(nextDate.getDate() + daysToAdd);
        }
        break;

      case 'monthly':
        nextDate = new Date(lastDate);
        nextDate.setMonth(nextDate.getMonth() + (patternConfig.interval || 1));
        
        // Handle specific day of month
        if (patternConfig.dayOfMonth) {
          nextDate.setDate(patternConfig.dayOfMonth);
          
          // Handle months with fewer days
          if (nextDate.getDate() !== patternConfig.dayOfMonth) {
            nextDate.setDate(0); // Last day of previous month
          }
        }
        
        // Handle week-based monthly (e.g., "2nd Tuesday of month")
        if (patternConfig.weekOfMonth && patternConfig.dayOfWeek !== undefined) {
          nextDate = this.calculateWeekBasedMonthly(nextDate, patternConfig.weekOfMonth, patternConfig.dayOfWeek);
        }
        break;

      case 'yearly':
        nextDate = new Date(lastDate);
        nextDate.setFullYear(nextDate.getFullYear() + (patternConfig.interval || 1));
        
        if (patternConfig.month) {
          nextDate.setMonth(patternConfig.month - 1); // Month is 1-based in config
        }
        
        if (patternConfig.dayOfMonth) {
          nextDate.setDate(patternConfig.dayOfMonth);
        }
        
        if (patternConfig.weekOfMonth && patternConfig.dayOfWeek !== undefined) {
          nextDate = this.calculateWeekBasedMonthly(nextDate, patternConfig.weekOfMonth, patternConfig.dayOfWeek);
        }
        break;

      case 'custom':
        // For custom patterns, we'd need to implement cron expression parsing
        // For now, default to daily
        nextDate = new Date(lastDate);
        nextDate.setDate(nextDate.getDate() + 1);
        break;

      default:
        nextDate = new Date(lastDate);
        nextDate.setDate(nextDate.getDate() + 1);
    }

    return nextDate.toISOString();
  }

  /**
   * Calculate week-based monthly dates (e.g., "2nd Tuesday of month")
   */
  private calculateWeekBasedMonthly(baseDate: Date, weekOfMonth: number, dayOfWeek: number): Date {
    const result = new Date(baseDate);
    result.setDate(1); // Start at first day of month
    
    // Find first occurrence of target day
    const firstDayOfMonth = result.getDay();
    const daysToAdd = (dayOfWeek - firstDayOfMonth + 7) % 7;
    result.setDate(1 + daysToAdd);
    
    // Add weeks to get to target week
    if (weekOfMonth === -1) {
      // Last occurrence of the day in the month
      result.setMonth(result.getMonth() + 1, 0); // Last day of month
      const lastDayOfMonth = result.getDay();
      const daysToSubtract = (lastDayOfMonth - dayOfWeek + 7) % 7;
      result.setDate(result.getDate() - daysToSubtract);
    } else {
      result.setDate(result.getDate() + (7 * (weekOfMonth - 1)));
    }
    
    return result;
  }

  /**
   * Handle execution failure and determine if recurrence should be paused
   */
  private async handleExecutionFailure(recurrence: TaskRecurrence): Promise<void> {
    const failureThreshold = 5; // Pause after 5 consecutive failures
    
    if (recurrence.failedExecutions >= failureThreshold) {
      console.log(`Pausing recurrence ${recurrence.id} due to ${recurrence.failedExecutions} consecutive failures`);
      
      await recurrenceService.updateRecurrence(recurrence.id, {
        status: 'error',
        isActive: false
      });
    }
  }

  /**
   * Mark a recurrence as completed
   */
  private async completeRecurrence(recurrenceId: string): Promise<void> {
    await recurrenceService.updateRecurrence(recurrenceId, {
      status: 'completed',
      isActive: false
    });
  }

  /**
   * Get scheduler status
   */
  getStatus(): { isRunning: boolean; checkInterval: number } {
    return {
      isRunning: this.isRunning,
      checkInterval: this.CHECK_INTERVAL_MS
    };
  }

  /**
   * Force execution of a specific recurrence (for testing/manual trigger)
   */
  async executeRecurrenceNow(recurrenceId: string): Promise<boolean> {
    try {
      const result = await recurrenceService.getRecurrence(recurrenceId);
      if (!result.success || !result.data) {
        console.error('Recurrence not found:', recurrenceId);
        return false;
      }

      await this.executeRecurrence(result.data);
      return true;
    } catch (error) {
      console.error('Error executing recurrence manually:', error);
      return false;
    }
  }
}

// Export singleton instance
export const recurrenceScheduler = new RecurrenceScheduler();

// Auto-start the scheduler in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  // Only start in browser environment and production
  recurrenceScheduler.start();
}
